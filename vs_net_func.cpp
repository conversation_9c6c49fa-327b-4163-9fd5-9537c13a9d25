/**
 *
 网络:
	1. 如果eth0可用,优先使用eth0
	2. 如果eth0断开,自动切换到eth1
	3. 支持双网口自动适应和负载均衡
	4. 保留内网搜索功能
 */
#include <sys/types.h>    
#include <sys/socket.h>    
#include <netinet/in.h>    
#include <arpa/inet.h>  
#include <sys/ioctl.h>
#include <linux/if.h>
#include <netdb.h> 
#include <netinet/if_ether.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <asm/types.h>
#include <linux/netlink.h>
#include <linux/rtnetlink.h>
#include <linux/ethtool.h>
#include <linux/sockios.h>
#include <netinet/ip_icmp.h>
#include <unistd.h>
#include <fcntl.h>

#include <unistd.h>
#include <sys/reboot.h>
#include <linux/reboot.h>  


#include "vs_comm_def.h"
#include "vs_net_func.h"
#include "vs_mem_buf.h"
#include "settings/vs_settings.h"
//#include "ipc/vsipc.h"
//#include "ipc/ptz_ctrl.h"
//#include "misc/vs_misc.h"
#include "json/vs_cJSON_utils.h"
#include "utils/vs_utils.h"
#include "app.h"

#include "ot_type.h"
#include "ss_mpi_sys.h"


static pthread_t	g_workThread = INVALID_HANDLE_VALUE;	// 工作线程


// 日志重定向线程
static UINT8 		g_logs_opened = FALSE;


// 网络检测线程
static INT32		g_fd_detect = 0;
static INT32		g_if_save_state = NET_ST_NONE;
static pthread_t	g_ifd_thread = INVALID_HANDLE_VALUE;

// 网络故障转移状态跟踪
static UINT32 		g_eth0_fail_count = 0;
static UINT32 		g_eth1_fail_count = 0;
static UINT32 		g_last_failover_time = 0;
static LPCSTR 		g_primary_interface = NET_ETH0;
static UINT8		g_dual_eth_mode = TRUE;		// 双网口模式
static UINT8		g_eth1_priority = FALSE;	// eth1优先级模式

// 智能IP分配相关状态
static UINT8		g_smart_ip_enabled = TRUE;	// 智能IP分配开关
static UINT8		g_network_segment_detected = FALSE;	// 网段检测完成标志
static UINT8		g_same_segment_detected = FALSE;	// 相同网段检测标志
static UINT32		g_last_segment_check_time = 0;	// 上次网段检测时间

// 启动时自动IP配置相关状态
static UINT8		g_auto_ip_config_enabled = TRUE;	// 启动时自动IP配置开关
static UINT8		g_startup_config_completed = FALSE;	// 启动配置完成标志
static UINT32		g_startup_config_time = 0;	// 启动配置时间

// net_load_config调用频率控制
static UINT32		g_eth0_load_config_count = 0;	// ETH0的net_load_config调用次数
static UINT32		g_eth1_load_config_count = 0;	// ETH1的net_load_config调用次数
static UINT32		g_max_load_config_calls = 2;	// 每个接口最大调用次数

// 网络配置文件严格对应关系管理
static UINT8		g_config_file_strict_mode = TRUE;	// 严格配置文件对应模式
static UINT32		g_last_eth0_config_save_time = 0;	// ETH0上次配置保存时间
static UINT32		g_last_eth1_config_save_time = 0;	// ETH1上次配置保存时间
static UINT8		g_eth0_config_auto_save = TRUE;		// ETH0自动保存开关
static UINT8		g_eth1_config_auto_save = TRUE;		// ETH1自动保存开关

// 函数声明（网络配置文件严格对应关系相关）
static UINT8 net_get_strict_config_path(LPCSTR if_name, CHAR *config_path);
static UINT8 net_auto_save_config_on_ready(LPCSTR if_name);
static UINT8 net_ensure_interface_independence(LPCSTR if_name, LPCSTR operation);

// 函数声明（热插拔功能相关）
static UINT8 net_cleanup_interface_on_disconnect(LPCSTR if_name);
static UINT8 net_is_interface_ready_for_hotplug_recovery(LPCSTR if_name);
static UINT8 net_handle_hotplug_recovery(LPCSTR if_name);


/**
 * 设置双网口模式
 */
VOID net_dual_eth_mode(UINT8 value)
{
	LOGW("%s(value=%d)", __FUNCTION__, value);
	g_dual_eth_mode = value;
}

UINT8 net_is_dual_eth_mode() {
	return g_dual_eth_mode;
}

/**
 * 设置eth1优先级模式
 */
VOID net_set_eth1_priority(UINT8 value)
{
	LOGW("%s(value=%d)", __FUNCTION__, value);
	g_eth1_priority = value;
}

UINT8 net_get_eth1_priority() {
	return g_eth1_priority;
}

/**
 * 设置智能IP分配模式
 */
VOID net_set_smart_ip_enabled(UINT8 value)
{
	LOGW("%s(value=%d)", __FUNCTION__, value);
	g_smart_ip_enabled = value;
}

UINT8 net_get_smart_ip_enabled() {
	return g_smart_ip_enabled;
}

/**
 * 设置启动时自动IP配置模式
 */
VOID net_set_auto_ip_config_enabled(UINT8 value)
{
	LOGW("%s(value=%d)", __FUNCTION__, value);
	g_auto_ip_config_enabled = value;
}

UINT8 net_get_auto_ip_config_enabled() {
	return g_auto_ip_config_enabled;
}

/**
 * 获取网口对应的严格配置文件路径
 * @param if_name 网络接口名称
 * @param config_path 返回配置文件路径
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_get_strict_config_path(LPCSTR if_name, CHAR *config_path)
{
	if (!if_name || !config_path) {
		LOGE("Invalid parameters for strict config path");
		return FALSE;
	}

	// 严格对应关系：ETH0 -> network.json, ETH1 -> network_1.json
	if (stricmp(if_name, NET_ETH0) == 0) {
		strcpy(config_path, CFG_NETWORK(CFG_PATH, 0));  // network.json
		LOGI("ETH0 strict config path: %s", config_path);
		return TRUE;
	} else if (stricmp(if_name, NET_ETH1) == 0) {
		strcpy(config_path, CFG_NETWORK(CFG_PATH, 1));  // network_1.json
		LOGI("ETH1 strict config path: %s", config_path);
		return TRUE;
	} else {
		LOGE("Unsupported interface for strict config: %s", if_name);
		return FALSE;
	}
}

/**
 * 检查配置文件是否存在（支持多个路径）
 * @param if_name 网络接口名称
 * @param config_path 返回找到的配置文件路径
 * @return 找到配置文件返回TRUE，否则返回FALSE
 */
UINT8 net_find_config_file(LPCSTR if_name, CHAR *config_path)
{
	if (!if_name || !config_path) {
		LOGE("Invalid parameters: if_name=%s, config_path=%p",
			 if_name ? if_name : "NULL", config_path);
		return FALSE;
	}

	CHAR config_path_str[128];

	// 使用严格配置文件对应关系
	if (g_config_file_strict_mode) {
		if (!net_get_strict_config_path(if_name, config_path_str)) {
			LOGE("Failed to get strict config path for %s", if_name);
			return FALSE;
		}

		LOGI("Searching strict config file for %s: %s", if_name, config_path_str);

		// 检查配置文件是否存在
		if (file_exist(config_path_str)) {
			strcpy(config_path, config_path_str);
			LOGI("Found strict config file for %s: %s", if_name, config_path_str);
			return TRUE;
		} else {
			LOGI("Strict config file not found: %s", config_path_str);
			// 在严格模式下，如果文件不存在，仍然返回正确的路径，以便后续可以创建
			strcpy(config_path, config_path_str);
			return FALSE;
		}
	} else {
		// 原始的非严格模式逻辑（向后兼容）
		CHAR primary_path[128];
		CHAR secondary_path[128];

		// 确定配置文件路径
		if (stricmp(if_name, NET_ETH0) == 0) {
			strcpy(primary_path, CFG_NETWORK(CFG_PATH, 0));
		} else if (stricmp(if_name, NET_ETH1) == 0) {
			strcpy(primary_path, CFG_NETWORK(CFG_PATH, 1));
		} else {
			LOGE("Unsupported interface: %s", if_name);
			return FALSE;
		}

		LOGI("Searching config file for %s: primary=%s, secondary=%s",
			 if_name, primary_path, secondary_path);

		// 优先检查 /tmp 路径
		if (file_exist(primary_path)) {
			strcpy(config_path, primary_path);
			LOGI("Found primary config file for %s: %s", if_name, primary_path);
			return TRUE;
		} else {
			LOGI("Primary config file not found: %s", primary_path);
		}

		// 检查 /etc 路径
		if (file_exist(secondary_path)) {
			strcpy(config_path, secondary_path);
			LOGI("Found secondary config file for %s: %s", if_name, secondary_path);
			return TRUE;
		} else {
			LOGI("Secondary config file not found: %s", secondary_path);
		}
	}

	LOGI("No config file found for %s, will use DHCP", if_name);
	return FALSE;
}

/**
 * 网络连接成功后自动保存配置到对应文件
 * @param if_name 网络接口名称
 * @return 保存成功返回TRUE，失败返回FALSE
 */
UINT8 net_auto_save_config_on_ready(LPCSTR if_name)
{
	if (!if_name) {
		LOGE("Invalid interface name for auto save");
		return FALSE;
	}

	// 检查是否启用自动保存
	UINT8 auto_save_enabled = FALSE;
	if (stricmp(if_name, NET_ETH0) == 0) {
		auto_save_enabled = g_eth0_config_auto_save;
	} else if (stricmp(if_name, NET_ETH1) == 0) {
		auto_save_enabled = g_eth1_config_auto_save;
	} else {
		LOGE("Unsupported interface for auto save: %s", if_name);
		return FALSE;
	}

	if (!auto_save_enabled) {
		LOGI("Auto save disabled for %s", if_name);
		return TRUE;
	}

	// 检查网络是否真正ready且有有效IP
	CHAR current_ip[32];
	if (!net_if_ready(if_name, current_ip) || strlen(current_ip) == 0) {
		LogE("Interface %s not ready or no IP, skipping auto save", if_name);
		return FALSE;
	}

	// 直接保存，不设置频率限制（网络连接成功时保存是合理的）

	LOGI("Auto saving network configuration for %s (IP: %s)", if_name, current_ip);

	// 获取严格对应的配置文件路径
	CHAR config_path[128];
	if (!net_get_strict_config_path(if_name, config_path)) {
		LOGE("Failed to get config path for auto save: %s", if_name);
		return FALSE;
	}

	// 保存当前有效配置到对应文件
	UINT8 is_eth1 = (stricmp(if_name, NET_ETH1) == 0) ? TRUE : FALSE;
	T_SET_NETWORK *net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

	// 使用settings_save_net函数保存配置
	settings_save_net(if_name);
	
	LOGI("Successfully auto saved config for %s to %s", if_name, config_path);

	// 更新保存时间
	if (last_save_time) {
		*last_save_time = current_time;
	}

	return TRUE;
	
}

/**
 * 确保网口配置独立性检查
 * @param if_name 当前操作的网络接口名称
 * @param operation 操作类型："load", "save", "connect", "disconnect"
 * @return 检查通过返回TRUE，否则返回FALSE
 */
UINT8 net_ensure_interface_independence(LPCSTR if_name, LPCSTR operation)
{
	if (!if_name || !operation) {
		LOGE("Invalid parameters for independence check");
		return FALSE;
	}

	LOGI("Checking interface independence for %s operation: %s", if_name, operation);

	// 确保配置文件严格对应
	CHAR config_path[128];
	if (!net_get_strict_config_path(if_name, config_path)) {
		LOGE("Failed to get strict config path for independence check: %s", if_name);
		return FALSE;
	}

	// 检查是否会影响其他网口
	LPCSTR other_if = NULL;
	if (stricmp(if_name, NET_ETH0) == 0) {
		other_if = NET_ETH1;
	} else if (stricmp(if_name, NET_ETH1) == 0) {
		other_if = NET_ETH0;
	} else {
		LOGE("Unsupported interface for independence check: %s", if_name);
		return FALSE;
	}

	// 记录其他网口的当前状态
	CHAR other_ip[32];
	UINT8 other_ready = net_if_ready(other_if, other_ip);

	LOGI("Independence check: %s ready=%d (IP: %s), operating on %s",
		 other_if, other_ready, other_ready ? other_ip : "none", if_name);

	// 在双网口模式下，确保操作不会影响其他网口的配置
	if (g_dual_eth_mode) {
		// 检查是否有配置冲突的风险
		if (strcmp(operation, "load") == 0 || strcmp(operation, "save") == 0) {
			// 配置加载/保存操作应该只影响对应的配置文件
			LOGI("Config operation %s for %s will not affect %s", operation, if_name, other_if);
		} else if (strcmp(operation, "connect") == 0) {
			// 连接操作不应该断开其他网口
			if (other_ready) {
				LOGI("Connect operation for %s will preserve %s connection", if_name, other_if);
			}
		} else if (strcmp(operation, "disconnect") == 0) {
			// 断开操作只影响当前网口
			LOGI("Disconnect operation for %s will not affect %s", if_name, other_if);
		}
	}

	LOGI("Interface independence check passed for %s", if_name);
	return TRUE;
}

/**
 * 应用DHCP配置到网络接口
 * @param if_name 网络接口名称
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_apply_dhcp_config(LPCSTR if_name)
{
	if (!if_name) {
		LOGE("Invalid interface name: NULL");
		return FALSE;
	}

	if (!net_dev_exist(if_name)) {
		LOGE("Interface %s does not exist", if_name);
		return FALSE;
	}

	LOGI("Applying DHCP configuration to %s", if_name);

	UINT8 is_eth1 = (stricmp(if_name, NET_ETH1) == 0) ? TRUE : FALSE;
	T_SET_NETWORK *net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

	// 清除现有配置
	LOGI("Clearing existing configuration for %s", if_name);
	QfSet0(net->ip, sizeof(net->ip));
	QfSet0(net->netmask, sizeof(net->netmask));
	QfSet0(net->gateway, sizeof(net->gateway));
	QfSet0(net->dns, sizeof(net->dns));

	// 设置为DHCP模式
	net->dhcp = TRUE;
	LOGI("Set %s to DHCP mode", if_name);

	// 先停止现有的DHCP客户端
	CHAR cmd[256];
	sprintf(cmd, "killall -9 udhcpc; pkill -f \"udhcpc.*%s\"", if_name);
	system_run(cmd);
	Sleep(500);  // 等待进程结束

	// 清除接口现有IP
	sprintf(cmd, "ifconfig %s 0.0.0.0", if_name);
	system_run(cmd);

	// 应用配置
	LOGI("Loading DHCP configuration for %s", if_name);
	INT32 result = net_load_config(if_name);
	if (result == OK) {
		// 等待DHCP获取IP地址
		LOGI("Waiting for DHCP to assign IP address to %s", if_name);
		Sleep(5000);  // 等待5秒让DHCP获取IP

		// 检查是否成功获取IP
		CHAR ip[32];
		if (net_if_ready(if_name, ip) && strlen(ip) > 0) {
			LOGI("DHCP successfully assigned IP %s to %s", ip, if_name);
			// 保存配置
			settings_save_net(if_name);
			return TRUE;
		} else {
			LOGW("DHCP did not assign IP to %s within timeout", if_name);
			// 即使没有立即获取到IP，也认为配置成功，DHCP可能需要更多时间
			settings_save_net(if_name);
			return TRUE;
		}
	} else {
		LOGE("Failed to load DHCP configuration for %s, result=%d", if_name, result);
		return FALSE;
	}
}

/**
 * 从配置文件加载网络配置
 * @param if_name 网络接口名称
 * @param config_file 配置文件路径
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_load_config_from_file(LPCSTR if_name, LPCSTR config_file)
{
	if (!if_name || !config_file) {
		LOGE("Invalid parameters: if_name=%s, config_file=%s",
			 if_name ? if_name : "NULL", config_file ? config_file : "NULL");
		return FALSE;
	}

	// 确保网口配置独立性
	if (!net_ensure_interface_independence(if_name, "load")) {
		LOGE("Interface independence check failed for %s", if_name);
		return FALSE;
	}

	if (!net_dev_exist(if_name)) {
		LOGE("Interface %s does not exist", if_name);
		return FALSE;
	}

	if (!file_exist(config_file)) {
		LOGE("Config file does not exist: %s", config_file);
		return FALSE;
	}

	// 验证配置文件路径的严格对应关系
	CHAR expected_config_path[128];
	if (g_config_file_strict_mode && net_get_strict_config_path(if_name, expected_config_path)) {
		if (stricmp(config_file, expected_config_path) != 0) {
			LOGW("Config file path mismatch for %s: expected=%s, actual=%s",
				 if_name, expected_config_path, config_file);
			// 在严格模式下，使用正确的配置文件路径
			strcpy((char*)config_file, expected_config_path);
			LOGI("Using strict config path: %s", config_file);
		}
	}

	LOGI("Loading network configuration for %s from %s", if_name, config_file);

	UINT8 is_eth1 = (stricmp(if_name, NET_ETH1) == 0) ? TRUE : FALSE;
	T_SET_NETWORK *net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

	// 备份当前配置
	T_SET_NETWORK backup_net;
	memcpy(&backup_net, net, sizeof(T_SET_NETWORK));

	// 从配置文件加载网络设置
	LOGI("Calling settings_load_network for %s", config_file);
	if (settings_load_network(config_file, net)) {
		LOGI("Successfully loaded network configuration from %s", config_file);
		LOGI("Config details: DHCP=%d, IP=%s, Netmask=%s, Gateway=%s, DNS=%s",
			 net->dhcp, net->ip, net->netmask, net->gateway, net->dns);

		// 验证配置有效性
		if (!net->dhcp) {
			// 静态IP模式，检查必要字段
			if (strlen(net->ip) == 0 || strlen(net->netmask) == 0) {
				LOGE("Invalid static IP configuration: IP=%s, Netmask=%s", net->ip, net->netmask);
				// 恢复备份配置
				memcpy(net, &backup_net, sizeof(T_SET_NETWORK));
				return FALSE;
			}
		}

		// 停止现有的网络配置
		CHAR cmd[256];
		sprintf(cmd, "killall -9 udhcpc; pkill -f \"udhcpc.*%s\"", if_name);
		system_run(cmd);
		Sleep(500);

		// 应用配置
		LOGI("Applying network configuration for %s", if_name);
		INT32 result = net_load_config(if_name);
		if (result == OK) {
			// 等待配置生效
			Sleep(2000);

			// 验证配置是否生效
			CHAR current_ip[32];
			if (net_if_ready(if_name, current_ip)) {
				LOGI("Network configuration applied successfully for %s, IP=%s", if_name, current_ip);

				// 自动保存当前有效配置到对应文件
				if (net_auto_save_config_on_ready(if_name)) {
					LOGI("Auto saved config for %s after successful connection", if_name);
				} else {
					LOGW("Failed to auto save config for %s", if_name);
				}

				// 保存配置到标准位置（向后兼容）
				settings_save_net(if_name);
				return TRUE;
			} else {
				LOGW("Configuration applied but interface not ready yet for %s", if_name);
				// 保存配置，可能需要更多时间生效
				settings_save_net(if_name);
				return TRUE;
			}
		} else {
			LOGE("Failed to apply network configuration for %s, result=%d", if_name, result);
			// 恢复备份配置
			memcpy(net, &backup_net, sizeof(T_SET_NETWORK));
			return FALSE;
		}
	} else {
		LOGE("Failed to load network configuration from %s", config_file);
		return FALSE;
	}
}

/**
 * 单个网口的自动IP配置
 * @param if_name 网络接口名称
 * @return 配置成功返回TRUE，失败返回FALSE
 */
UINT8 net_auto_config_single_interface(LPCSTR if_name)
{
	if (!if_name) {
		LOGE("Invalid interface name: NULL");
		return FALSE;
	}

	// 确保网口配置独立性
	if (!net_ensure_interface_independence(if_name, "connect")) {
		LOGE("Interface independence check failed for %s", if_name);
		return FALSE;
	}

	if (!net_dev_exist(if_name)) {
		LOGE("Interface %s does not exist", if_name);
		return FALSE;
	}

	// 检查物理连接状态
	UINT8 carrier = net_dev_carrier(if_name);
	LOGI("Interface %s carrier status: %d", if_name, carrier);

	if (!carrier) {
		LOGI("Interface %s has no physical connection, skipping configuration", if_name);
		return FALSE;
	}

	LOGI("Starting auto IP configuration for %s (carrier detected)", if_name);

	// 检查是否已经配置
	CHAR current_ip[32];
	if (net_if_ready(if_name, current_ip) && strlen(current_ip) > 0) {
		LOGI("Interface %s already configured with IP: %s", if_name, current_ip);
		return TRUE;
	}

	// 1. 优先检查配置文件
	CHAR config_file[128];
	LOGI("Searching for config file for %s", if_name);
	if (net_find_config_file(if_name, config_file)) {
		LOGI("Found config file for %s: %s", if_name, config_file);

		if (net_load_config_from_file(if_name, config_file)) {
			LOGI("Successfully configured %s using config file", if_name);

			// 验证配置是否生效
			Sleep(3000);  // 等待配置生效
			if (net_if_ready(if_name, current_ip)) {
				LOGI("Config file configuration verified for %s, IP: %s", if_name, current_ip);
				return TRUE;
			} else {
				LOGW("Config file applied but IP not ready for %s", if_name);
				return TRUE;  // 配置可能需要更多时间生效
			}
		} else {
			LOGW("Failed to load config from file, falling back to DHCP for %s", if_name);
		}
	} else {
		LOGI("No config file found for %s", if_name);
	}

	// 2. 回退到DHCP模式
	LOGI("Using DHCP configuration for %s", if_name);
	if (net_apply_dhcp_config(if_name)) {
		LOGI("Successfully applied DHCP configuration for %s", if_name);

		// 验证DHCP配置是否生效
		Sleep(5000);  // 等待DHCP获取IP
		if (net_if_ready(if_name, current_ip)) {
			LOGI("DHCP configuration verified for %s, IP: %s", if_name, current_ip);

			// 自动保存DHCP获取的配置到对应文件
			if (net_auto_save_config_on_ready(if_name)) {
				LOGI("Auto saved DHCP config for %s after successful connection", if_name);
			} else {
				LOGW("Failed to auto save DHCP config for %s", if_name);
			}

			return TRUE;
		} else {
			LOGW("DHCP applied but IP not ready for %s", if_name);
			return TRUE;  // DHCP可能需要更多时间
		}
	} else {
		LOGE("Failed to configure %s with DHCP", if_name);
		return FALSE;
	}
}

/**
 * 双网口启动时自动IP配置
 * @return 配置成功的接口数量
 */
INT32 net_startup_auto_ip_config()
{
	if (!g_auto_ip_config_enabled) {
		LOGI("Startup auto IP configuration is disabled");
		return 0;
	}

	if (g_startup_config_completed) {
		LOGI("Startup auto IP configuration already completed");
		return 0;
	}

	LOGI("Starting dual ethernet auto IP configuration...");

	INT32 configured_count = 0;
	UINT8 eth0_configured = FALSE;
	UINT8 eth1_configured = FALSE;

	// 等待网络接口稳定
	Sleep(1000);

	// 检测并配置ETH0
	if (net_dev_exist(NET_ETH0)) {
		LOGI("Detecting ETH0 interface...");

		if (net_dev_carrier(NET_ETH0)) {
			LOGI("ETH0 has physical connection, configuring...");

			if (net_auto_config_single_interface(NET_ETH0)) {
				eth0_configured = TRUE;
				configured_count++;
				LOGI("ETH0 configuration completed successfully");
			} else {
				LOGE("ETH0 configuration failed");
			}
		} else {
			LOGI("ETH0 has no physical connection");
		}
	} else {
		LOGI("ETH0 interface does not exist");
	}

	// 检测并配置ETH1（如果启用双网口模式）
	if (g_dual_eth_mode && net_dev_exist(NET_ETH1)) {
		LOGI("Detecting ETH1 interface...");

		if (net_dev_carrier(NET_ETH1)) {
			LOGI("ETH1 has physical connection, configuring...");

			// 如果启用了智能IP分配，需要考虑网段冲突
			if (g_smart_ip_enabled && eth0_configured) {
				LOGI("Smart IP allocation enabled, checking for segment conflicts...");

				// 延迟一段时间让ETH0配置稳定
				Sleep(3000);

				// 执行智能检测
				INT32 smart_result = net_smart_ip_allocation_detect();
				if (smart_result == NET_ST_ETH0) {
					LOGW("Same network segment detected, skipping ETH1 configuration to avoid conflicts");
					LOGI("ETH1 configuration skipped due to same segment detection");
				} else {
					// 不同网段或检测失败，继续配置ETH1
					if (net_auto_config_single_interface(NET_ETH1)) {
						eth1_configured = TRUE;
						configured_count++;
						LOGI("ETH1 configuration completed successfully");
					} else {
						LOGE("ETH1 configuration failed");
					}
				}
			} else {
				// 没有启用智能IP分配或ETH0未配置，直接配置ETH1
				if (net_auto_config_single_interface(NET_ETH1)) {
					eth1_configured = TRUE;
					configured_count++;
					LOGI("ETH1 configuration completed successfully");
				} else {
					LOGE("ETH1 configuration failed");
				}
			}
		} else {
			LOGI("ETH1 has no physical connection");
		}
	} else {
		if (!g_dual_eth_mode) {
			LOGI("Dual ethernet mode is disabled, skipping ETH1");
		} else {
			LOGI("ETH1 interface does not exist");
		}
	}

	// 更新网络状态
	if (eth0_configured && eth1_configured) {
		g_if_save_state = NET_ST_DUAL_ETH;
		LOGI("Dual ethernet mode activated");
	} else if (eth0_configured) {
		g_if_save_state = NET_ST_ETH0;
		LOGI("ETH0 single mode activated");
	} else if (eth1_configured) {
		g_if_save_state = NET_ST_ETH1;
		LOGI("ETH1 single mode activated");
	} else {
		g_if_save_state = NET_ST_NONE;
		LOGW("No network interfaces configured");
	}

	// 标记启动配置完成
	g_startup_config_completed = TRUE;
	g_startup_config_time = get_app_uptime();

	LOGI("Startup auto IP configuration completed: %d interfaces configured", configured_count);

	// 如果有接口配置成功，同步时间
	if (configured_count > 0) {
		sync_time(TIME_CLOCK_SVR, FALSE);
	}

	return configured_count;
}

/**
 * 简化的网络配置处理（基于物理连接检测）
 * @return 配置成功的接口数量
 */
INT32 net_simplified_auto_config()
{
	if (!g_auto_ip_config_enabled) {
		LOGI("Auto IP configuration is disabled");
		return 0;
	}

	LOGI("Starting simplified auto network configuration...");

	// 检测物理连接状态
	UINT8 eth0_carrier = net_dev_exist(NET_ETH0) ? net_dev_carrier(NET_ETH0) : FALSE;
	UINT8 eth1_carrier = (g_dual_eth_mode && net_dev_exist(NET_ETH1)) ? net_dev_carrier(NET_ETH1) : FALSE;

	LOGI("Physical connection detection: ETH0=%d, ETH1=%d", eth0_carrier, eth1_carrier);

	INT32 configured_count = 0;
	static UINT8 eth0_config_called = FALSE;
	static UINT8 eth1_config_called = FALSE;

	// 单网卡插入场景处理
	if (eth0_carrier && !eth1_carrier) {
		LOGI("Single interface scenario: ETH0 connected");
		if (!eth0_config_called) {
			if (net_configure_single_interface(NET_ETH0)) {
				configured_count++;
				g_if_save_state = NET_ST_ETH0;
				LOGI("ETH0 configured successfully");
			}
			eth0_config_called = TRUE;
		}
	}
	else if (!eth0_carrier && eth1_carrier) {
		LOGI("Single interface scenario: ETH1 connected");
		if (!eth1_config_called) {
			if (net_configure_single_interface(NET_ETH1)) {
				configured_count++;
				g_if_save_state = NET_ST_ETH1;
				LOGI("ETH1 configured successfully");
			}
			eth1_config_called = TRUE;
		}
	}
	// 双网卡插入场景处理
	else if (eth0_carrier && eth1_carrier) {
		LOGI("Dual interface scenario: Both ETH0 and ETH1 connected");

		// 检查智能IP分配设置
		if (g_smart_ip_enabled) {
			LOGI("Smart IP allocation enabled, checking for segment conflicts");

			// 执行智能检测
			INT32 smart_result = net_smart_ip_allocation_detect();
			if (smart_result == NET_ST_ETH0) {
				LOGW("Same network segment detected, configuring ETH0 only");
				if (!eth0_config_called) {
					if (net_configure_single_interface(NET_ETH0)) {
						configured_count++;
						g_if_save_state = NET_ST_ETH0;
					}
					eth0_config_called = TRUE;
				}
			} else {
				// 不同网段或检测失败，配置两个接口
				LOGI("Different segments or detection failed, configuring both interfaces");
				if (!eth0_config_called) {
					if (net_configure_single_interface(NET_ETH0)) {
						configured_count++;
					}
					eth0_config_called = TRUE;
				}
				if (!eth1_config_called) {
					if (net_configure_single_interface(NET_ETH1)) {
						configured_count++;
					}
					eth1_config_called = TRUE;
				}
				if (configured_count > 0) {
					g_if_save_state = NET_ST_DUAL_ETH;
				}
			}
		} else {
			// 默认DHCP模式配置两个接口
			LOGI("Smart IP allocation disabled, using default DHCP for both interfaces");
			if (!eth0_config_called) {
				if (net_configure_single_interface(NET_ETH0)) {
					configured_count++;
				}
				eth0_config_called = TRUE;
			}
			if (!eth1_config_called) {
				if (net_configure_single_interface(NET_ETH1)) {
					configured_count++;
				}
				eth1_config_called = TRUE;
			}
			if (configured_count > 0) {
				g_if_save_state = NET_ST_DUAL_ETH;
			}
		}
	}
	else {
		LOGI("No physical connections detected");
		g_if_save_state = NET_ST_NONE;
	}

	LOGI("Simplified auto configuration completed: %d interfaces configured", configured_count);

	// 如果有接口配置成功，同步时间
	if (configured_count > 0) {
		sync_time(TIME_CLOCK_SVR, FALSE);
	}

	return configured_count;
}

/**
 * 单接口配置处理（配置文件优先 → DHCP默认）
 * @param if_name 网络接口名称
 * @return 配置成功返回TRUE，失败返回FALSE
 */
UINT8 net_configure_single_interface(LPCSTR if_name)
{
	if (!if_name || !net_dev_exist(if_name)) {
		LOGE("Invalid interface: %s", if_name ? if_name : "NULL");
		return FALSE;
	}

	// 检查物理连接状态
	if (!net_dev_carrier(if_name)) {
		LOGI("Interface %s has no physical connection", if_name);
		return FALSE;
	}

	LOGI("Configuring interface %s with strict config file priority", if_name);

	// 严格检查是否已经配置 - 防止意外清理已配置接口
	CHAR current_ip[32];
	if (net_if_ready(if_name, current_ip) && strlen(current_ip) > 0) {
		LOGI("Interface %s already configured with IP: %s, skipping reconfiguration", if_name, current_ip);

		// 检查是否有配置文件要求重新配置
		CHAR config_file[128];
		if (net_find_config_file(if_name, config_file)) {
			LOGI("Config file exists for %s, but interface already configured - maintaining current configuration", if_name);
		}

		return TRUE;
	}

	// 1. 配置文件优先
	CHAR config_file[128];
	if (net_find_config_file(if_name, config_file)) {
		LOGI("Found config file for %s: %s", if_name, config_file);

		if (net_load_config_from_file(if_name, config_file)) {
			LOGI("Successfully configured %s using config file", if_name);

			// 使用受限制的net_load_config调用
			INT32 result = net_load_config_limited(if_name);
			if (result == OK) {
				LOGI("net_load_config_limited called successfully for %s", if_name);
				Sleep(3000);  // 等待配置生效

				if (net_if_ready(if_name, current_ip)) {
					LOGI("Config file configuration verified for %s, IP: %s", if_name, current_ip);
					return TRUE;
				} else {
					LOGW("Config file applied but IP not ready for %s", if_name);
					return TRUE;  // 配置可能需要更多时间生效
				}
			} 
			else if (result == -1) {
				LOGW("net_load_config call limit exceeded for %s", if_name);
				return FALSE;
			} 
			else {
				LOGE("net_load_config_limited failed for %s, result=%d", if_name, result);
			}
		} 
		else {
			LOGW("Failed to load config from file for %s", if_name);
		}
	} 
	else {
		LOGI("No config file found for %s", if_name);
	}

	// 2. DHCP默认模式
	LOGI("Using DHCP default mode for %s", if_name);
	if (net_apply_dhcp_config(if_name)) {
		LOGI("Successfully applied DHCP configuration for %s", if_name);

		// 使用受限制的net_load_config调用
		INT32 result = net_load_config_limited(if_name);
		if (result == OK) {
			LOGI("net_load_config_limited called successfully for %s (DHCP)", if_name);
			Sleep(5000);  // 等待DHCP获取IP

			if (net_if_ready(if_name, current_ip)) {
				LOGI("DHCP configuration verified for %s, IP: %s", if_name, current_ip);
				return TRUE;
			} 
			else {
				LOGW("DHCP applied but IP not ready for %s", if_name);
				return TRUE;  // DHCP可能需要更多时间
			}
		} 
		else if (result == -1) {
			LOGW("net_load_config call limit exceeded for %s (DHCP)", if_name);
			return FALSE;
		} 
		else {
			LOGE("net_load_config_limited failed for %s (DHCP), result=%d", if_name, result);
		}
	} 
	else {
		LOGE("Failed to apply DHCP configuration for %s", if_name);
	}

	return FALSE;
}


/**
 * 严格应用配置文件设置（不允许自动切换）
 * @param if_name 网络接口名称
 * @param config_file 配置文件路径
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_apply_strict_config_file(LPCSTR if_name, LPCSTR config_file)
{
	if (!if_name || !config_file) {
		LOGE("Invalid parameters for strict config file application");
		return FALSE;
	}

	if (!file_exist(config_file)) {
		LOGE("Config file does not exist: %s", config_file);
		return FALSE;
	}

	LOGI("Applying strict config file for %s from %s", if_name, config_file);

	UINT8 is_eth1 = (stricmp(if_name, NET_ETH1) == 0) ? TRUE : FALSE;
	T_SET_NETWORK *net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

	// 备份当前配置
	T_SET_NETWORK backup_net;
	memcpy(&backup_net, net, sizeof(T_SET_NETWORK));

	// 从配置文件加载网络设置
	if (!settings_load_network(config_file, net)) {
		LOGE("Failed to load network configuration from %s", config_file);
		return FALSE;
	}

	LOGI("Loaded config: DHCP=%d, IP=%s, Netmask=%s, Gateway=%s",
		 net->dhcp, net->ip, net->netmask, net->gateway);

	// 验证配置有效性
	if (!net->dhcp) {
		// 静态IP模式，检查必要字段
		if (strlen(net->ip) == 0 || strlen(net->netmask) == 0) {
			LOGE("Invalid static IP configuration in file: IP=%s, Netmask=%s", net->ip, net->netmask);
			memcpy(net, &backup_net, sizeof(T_SET_NETWORK));
			return FALSE;
		}
		LOGI("Static IP configuration detected - will apply regardless of connectivity");
	} 
	else {
		LOGI("DHCP configuration detected - will apply regardless of IP acquisition result");
	}

	// 停止现有的网络配置（仅针对当前接口）
	CHAR cmd[256];
	sprintf(cmd, "killall -9 udhcpc; pkill -f \"udhcpc.*%s\"", if_name);
	system_run(cmd);
	Sleep(500);

	// 使用受限制的配置加载
	INT32 result = net_load_config_limited(if_name);
	if (result == OK) {
		LOGI("Strict config file applied successfully for %s", if_name);

		// 等待配置生效（不检查连通性）
		Sleep(3000);

		// 保存配置到标准位置
		settings_save_net(if_name);

		// 验证配置是否应用（不要求连通性）
		CHAR applied_ip[32];
		if (net_if_ready(if_name, applied_ip)) {
			LOGI("Config file configuration applied for %s, IP: %s", if_name, applied_ip);
		} else {
			LOGI("Config file configuration applied for %s, waiting for IP assignment", if_name);
		}

		return TRUE;
	} 
	else if (result == -1) {
		LOGW("net_load_config call limit exceeded for %s", if_name);
		memcpy(net, &backup_net, sizeof(T_SET_NETWORK));
		return FALSE;
	} 
	else {
		LOGE("Failed to apply strict config file for %s, result=%d", if_name, result);
		memcpy(net, &backup_net, sizeof(T_SET_NETWORK));
		return FALSE;
	}
}


/**
 * 严格应用DHCP配置（不允许自动切换）
 * @param if_name 网络接口名称
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_apply_strict_dhcp_config(LPCSTR if_name)
{
	if (!if_name) {
		LOGE("Invalid interface name for strict DHCP config");
		return FALSE;
	}

	if (!net_dev_exist(if_name)) {
		LOGE("Interface %s does not exist", if_name);
		return FALSE;
	}

	LOGI("Applying strict DHCP configuration to %s", if_name);

	UINT8 is_eth1 = (stricmp(if_name, NET_ETH1) == 0) ? TRUE : FALSE;
	T_SET_NETWORK *net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

	// 清除现有配置
	LOGI("Clearing existing configuration for %s", if_name);
	QfSet0(net->ip, sizeof(net->ip));
	QfSet0(net->netmask, sizeof(net->netmask));
	QfSet0(net->gateway, sizeof(net->gateway));
	QfSet0(net->dns, sizeof(net->dns));

	// 设置为DHCP模式
	net->dhcp = TRUE;
	LOGI("Set %s to strict DHCP mode", if_name);

	// 先停止现有的DHCP客户端（仅针对当前接口）
	CHAR cmd[256];
	sprintf(cmd, "killall -9 udhcpc; pkill -f \"udhcpc.*%s\"", if_name);
	system_run(cmd);
	Sleep(500);

	// 使用受限制的配置加载
	INT32 result = net_load_config_limited(if_name);
	if (result == OK) {
		LOGI("Strict DHCP configuration applied for %s", if_name);

		// 等待DHCP尝试获取IP（不要求成功）
		Sleep(5000);

		// 保存配置
		settings_save_net(if_name);

		// 检查DHCP结果（不影响返回值）
		CHAR dhcp_ip[32];
		if (net_if_ready(if_name, dhcp_ip) && strlen(dhcp_ip) > 0) {
			LOGI("DHCP successfully assigned IP %s to %s", dhcp_ip, if_name);
		} else {
			LOGI("DHCP configuration applied to %s, IP assignment may take more time", if_name);
		}

		return TRUE;  // 只要配置应用成功就返回TRUE，不要求IP获取成功
	} 
	else if (result == -1) {
		LOGW("net_load_config call limit exceeded for %s (DHCP)", if_name);
		return FALSE;
	} 
	else {
		LOGE("Failed to apply strict DHCP configuration for %s, result=%d", if_name, result);
		return FALSE;
	}
}


/**
 * 严格的配置文件优先配置处理
 * @param if_name 网络接口名称
 * @return 配置成功返回TRUE，失败返回FALSE
 */
UINT8 net_configure_with_strict_priority(LPCSTR if_name)
{
	if (!if_name || !net_dev_exist(if_name)) {
		LOGE("Invalid interface: %s", if_name ? if_name : "NULL");
		return FALSE;
	}

	// 检查物理连接状态
	if (!net_dev_carrier(if_name)) {
		LOGI("Interface %s has no physical connection", if_name);
		return FALSE;
	}

	LOGI("Configuring interface %s with strict config file priority", if_name);

	// 严格检查是否已经配置 - 防止意外清理已配置接口
	CHAR current_ip[32];
	if (net_if_ready(if_name, current_ip) && strlen(current_ip) > 0) {
		LOGI("Interface %s already configured with IP: %s, maintaining current configuration", if_name, current_ip);
		return TRUE;
	}

	// 1. 严格的配置文件优先策略
	CHAR config_file[128];
	if (net_find_config_file(if_name, config_file)) {
		LOGI("Found config file for %s: %s - applying strict configuration", if_name, config_file);

		if (net_apply_strict_config_file(if_name, config_file)) {
			LOGI("Successfully applied strict config file for %s", if_name);
			return TRUE;
		} 
		else {
			LOGE("Failed to apply strict config file for %s", if_name);
			return FALSE;  // 配置文件存在但失败，不回退到DHCP
		}
	} 
	else {
		LOGI("No config file found for %s, using DHCP default", if_name);
	}

	// 2. 仅在没有配置文件时使用DHCP默认模式
	LOGI("Using DHCP default mode for %s (no config file)", if_name);
	if (net_apply_strict_dhcp_config(if_name)) {
		LOGI("Successfully applied DHCP configuration for %s", if_name);
		return TRUE;
	} 
	else {
		LOGE("Failed to apply DHCP configuration for %s", if_name);
		return FALSE;
	}
}



/**
 * 简化的启动配置线程函数
 * @param arg 线程参数（未使用）
 * @return NULL
 */
LPVOID net_simplified_startup_thread(LPVOID arg)
{
	LOGI("Simplified startup auto IP configuration thread started");

	// 等待系统稳定
	Sleep(2000);

	// 执行简化的自动配置
	INT32 result = net_simplified_auto_config();

	LOGI("Simplified startup auto IP configuration thread completed, configured %d interfaces", result);

	return NULL;
}

/**
 * 受限制的net_load_config调用（限制调用频率）
 * @param if_name 网络接口名称
 * @return 成功=OK；失败=FAIL；超出限制=-1
 */
INT32 net_load_config_limited(LPCSTR if_name)
{
	if (!if_name) {
		LOGE("Interface name cannot be NULL for limited config loading");
		return FAIL;
	}

	// 检查调用次数限制
	UINT32 *call_count = NULL;
	if (stricmp(if_name, NET_ETH0) == 0) {
		call_count = &g_eth0_load_config_count;
	} 
	else if (stricmp(if_name, NET_ETH1) == 0) {
		call_count = &g_eth1_load_config_count;
	} 
	else {
		LOGE("Unsupported interface for limited config: %s", if_name);
		return FAIL;
	}

	if (*call_count >= g_max_load_config_calls) {
		LOGW("net_load_config call limit exceeded for %s (count: %d, limit: %d)",
			 if_name, *call_count, g_max_load_config_calls);
		return -1;  // 超出限制
	}

	// 增加调用计数
	(*call_count)++;
	LOGI("Calling net_load_config for %s (call count: %d/%d)",
		 if_name, *call_count, g_max_load_config_calls);

	// 调用原始函数
	INT32 result = net_load_config(if_name);

	LOGI("net_load_config result for %s: %d", if_name, result);
	return result;
}

/**
 * 重置net_load_config调用计数器
 */
VOID net_reset_load_config_counters()
{
	LOGI("Resetting net_load_config call counters");
	g_eth0_load_config_count = 0;
	g_eth1_load_config_count = 0;
}

/**
 * 获取net_load_config调用统计信息
 * @param eth0_count 返回ETH0调用次数
 * @param eth1_count 返回ETH1调用次数
 */
VOID net_get_load_config_stats(UINT32 *eth0_count, UINT32 *eth1_count)
{
	if (eth0_count) *eth0_count = g_eth0_load_config_count;
	if (eth1_count) *eth1_count = g_eth1_load_config_count;
}

/**
 * 启动配置线程函数
 * @param arg 线程参数（未使用）
 * @return NULL
 */
LPVOID net_startup_config_thread(LPVOID arg)
{
	LOGI("Startup auto IP configuration thread started");

	// 等待系统稳定
	Sleep(2000);

	// 执行自动配置
	INT32 result = net_startup_auto_ip_config();

	LOGI("Startup auto IP configuration thread completed, configured %d interfaces", result);

	return NULL;
}

/**
 * 热插拔触发的自动配置
 * @param if_name 网络接口名称
 * @return 配置成功返回TRUE，失败返回FALSE
 */
UINT8 net_hotplug_auto_config(LPCSTR if_name)
{
	if (!g_auto_ip_config_enabled) {
		LOGI("Auto IP configuration is disabled, skipping hotplug config for %s",
			 if_name ? if_name : "NULL");
		return FALSE;
	}

	if (!if_name) {
		LOGE("Invalid interface name for hotplug config");
		return FALSE;
	}

	LOGI("Hotplug auto configuration triggered for %s", if_name);

	// 检查接口是否存在
	if (!net_dev_exist(if_name)) {
		LOGE("Interface %s does not exist for hotplug config", if_name);
		return FALSE;
	}

	// 检查物理连接
	if (!net_dev_carrier(if_name)) {
		LOGI("Interface %s has no carrier for hotplug config", if_name);
		return FALSE;
	}

	// 检查是否已经配置过
	CHAR current_ip[32];
	if (net_if_ready(if_name, current_ip) && strlen(current_ip) > 0) {
		LOGI("Interface %s already configured and ready with IP: %s", if_name, current_ip);
		return TRUE;
	}

	LOGI("Interface %s needs configuration, starting hotplug auto config", if_name);

	// 延迟一段时间让网络稳定
	Sleep(2000);

	// 再次检查物理连接（可能在延迟期间断开）
	if (!net_dev_carrier(if_name)) {
		LOGI("Interface %s lost carrier during hotplug delay", if_name);
		return FALSE;
	}

	// 执行简化的单接口配置
	LOGI("Executing simplified configuration for %s", if_name);
	if (net_configure_single_interface(if_name)) {
		LOGI("Hotplug auto configuration successful for %s", if_name);

		// 更新网络状态
		LOGI("Updating network state after hotplug config for %s", if_name);
		if (stricmp(if_name, NET_ETH0) == 0) {
			if (g_dual_eth_mode && net_if_ready(NET_ETH1, NULL)) {
				g_if_save_state = NET_ST_DUAL_ETH;
				LOGI("Network state updated to DUAL_ETH after ETH0 hotplug");
			} else {
				g_if_save_state = NET_ST_ETH0;
				LOGI("Network state updated to ETH0 after hotplug");
			}
		} else if (stricmp(if_name, NET_ETH1) == 0) {
			if (net_if_ready(NET_ETH0, NULL)) {
				g_if_save_state = NET_ST_DUAL_ETH;
				LOGI("Network state updated to DUAL_ETH after ETH1 hotplug");
			} else {
				g_if_save_state = NET_ST_ETH1;
				LOGI("Network state updated to ETH1 after hotplug");
			}
		}

		// 同步时间
		sync_time(TIME_CLOCK_SVR, FALSE);

		return TRUE;
	} else {
		LOGE("Hotplug auto configuration failed for %s", if_name);
		return FALSE;
	}
}

/**
 * 检测网络接口的网段信息
 * @param if_name 网络接口名称
 * @param network_info 返回网段信息结构体
 * @return 成功返回TRUE，失败返回FALSE
 */
typedef struct {
	CHAR ip[32];
	CHAR netmask[32];
	CHAR gateway[32];
	CHAR network[32];		// 网络地址
	UINT32 network_addr;	// 网络地址数值
	UINT32 netmask_addr;	// 子网掩码数值
} T_NETWORK_SEGMENT_INFO;

UINT8 net_detect_network_segment(LPCSTR if_name, T_NETWORK_SEGMENT_INFO *network_info)
{
	if (!if_name || !network_info) return FALSE;

	QfSet0(network_info, sizeof(T_NETWORK_SEGMENT_INFO));

	// 检查接口是否存在且有物理连接
	if (!net_dev_exist(if_name) || !net_dev_carrier(if_name)) {
		LogE("Interface %s not exist or no carrier", if_name);
		return FALSE;
	}

	// 尝试通过DHCP discover获取网段信息
	CHAR cmd[256];
	CHAR result_file[64];
	sprintf(result_file, "/tmp/dhcp_discover_%s.txt", if_name);

	// 使用dhcping或nmap进行网段探测
	sprintf(cmd, "timeout 5 nmap -sn %s/24 2>/dev/null | grep 'Nmap scan report' | head -5 > %s",
			"***********", result_file);

	// 先尝试获取当前IP信息（如果已配置）
	CHAR current_ip[32] = {0};
	if (net_if_ready(if_name, current_ip) && strlen(current_ip) > 0) {
		strcpy(network_info->ip, current_ip);

		// 获取子网掩码
		int skfd = socket(AF_INET, SOCK_DGRAM, 0);
		if (skfd >= 0) {
			struct ifreq ifr;
			strcpy(ifr.ifr_name, if_name);
			if (ioctl(skfd, SIOCGIFNETMASK, &ifr) >= 0) {
				struct sockaddr_in *netmask = (struct sockaddr_in *)&ifr.ifr_netmask;
				strcpy(network_info->netmask, inet_ntoa(netmask->sin_addr));
				network_info->netmask_addr = netmask->sin_addr.s_addr;
			}
			close(skfd);
		}

		// 获取网关地址
		net_gw_addr(if_name, network_info->gateway);

		// 计算网络地址
		if (strlen(network_info->netmask) > 0) {
			struct in_addr ip_addr, mask_addr, net_addr;
			inet_aton(network_info->ip, &ip_addr);
			inet_aton(network_info->netmask, &mask_addr);
			net_addr.s_addr = ip_addr.s_addr & mask_addr.s_addr;
			strcpy(network_info->network, inet_ntoa(net_addr));
			network_info->network_addr = net_addr.s_addr;
		}

		LOGI("Detected network segment for %s: IP=%s, Netmask=%s, Gateway=%s, Network=%s",
			 if_name, network_info->ip, network_info->netmask,
			 network_info->gateway, network_info->network);
		return TRUE;
	}

	// 如果没有IP，尝试通过ARP扫描检测网段
	LOGI("No IP configured for %s, attempting network segment detection", if_name);
	return FALSE;
}

/**
 * 比较两个网络接口是否在相同网段
 * @param info1 第一个网络接口信息
 * @param info2 第二个网络接口信息
 * @return 相同网段返回TRUE，不同网段返回FALSE
 */
UINT8 net_is_same_network_segment(T_NETWORK_SEGMENT_INFO *info1, T_NETWORK_SEGMENT_INFO *info2)
{
	if (!info1 || !info2) return FALSE;

	// 如果任一接口没有有效的网络信息，认为不在同一网段
	if (strlen(info1->network) == 0 || strlen(info2->network) == 0) {
		return FALSE;
	}

	// 比较网络地址
	if (info1->network_addr == info2->network_addr &&
		info1->netmask_addr == info2->netmask_addr) {
		LOGI("Same network segment detected: %s == %s", info1->network, info2->network);
		return TRUE;
	}

	// 额外检查：比较网关地址
	if (strlen(info1->gateway) > 0 && strlen(info2->gateway) > 0) {
		if (strcmp(info1->gateway, info2->gateway) == 0) {
			LOGI("Same gateway detected: %s == %s", info1->gateway, info2->gateway);
			return TRUE;
		}
	}

	LOGI("Different network segments: %s != %s", info1->network, info2->network);
	return FALSE;
}

/**
 * 双网口智能IP分配检测
 * @return 返回分配策略：NET_ST_DUAL_ETH(不同网段), NET_ST_ETH0(相同网段), NET_ST_NONE(检测失败)
 */
INT32 net_smart_ip_allocation_detect()
{
	if (!g_smart_ip_enabled || !g_dual_eth_mode) {
		return NET_ST_NONE;
	}

	UINT32 current_time = get_app_uptime();

	// 防止频繁检测 - 至少间隔30秒
	if (g_network_segment_detected &&
		current_time - g_last_segment_check_time < 30) {
		return g_same_segment_detected ? NET_ST_ETH0 : NET_ST_DUAL_ETH;
	}

	LOGI("Starting smart IP allocation detection...");

	// 检查两个网口的物理连接状态
	UINT8 eth0_carrier = net_dev_carrier(NET_ETH0);
	UINT8 eth1_carrier = net_dev_carrier(NET_ETH1);

	if (!eth0_carrier || !eth1_carrier) {
		LOGI("Physical connection check: ETH0=%d, ETH1=%d - not both connected",
			 eth0_carrier, eth1_carrier);
		g_network_segment_detected = FALSE;
		return NET_ST_NONE;
	}

	LOGI("Both interfaces have physical connection, detecting network segments...");

	T_NETWORK_SEGMENT_INFO eth0_info, eth1_info;
	UINT8 eth0_detected = net_detect_network_segment(NET_ETH0, &eth0_info);
	UINT8 eth1_detected = net_detect_network_segment(NET_ETH1, &eth1_info);

	g_last_segment_check_time = current_time;

	// 如果两个接口都没有检测到网段信息，尝试DHCP探测
	if (!eth0_detected && !eth1_detected) {
		LOGI("No network segment detected on both interfaces, attempting DHCP discovery...");

		// 临时启动DHCP客户端进行网段探测（不实际配置IP）
		CHAR cmd[256];

		// 对eth0进行DHCP discover
		sprintf(cmd, "timeout 10 dhclient -1 -d %s >/tmp/dhcp_eth0.log 2>&1 &", NET_ETH0);
		system_no_fd(cmd);

		// 对eth1进行DHCP discover
		sprintf(cmd, "timeout 10 dhclient -1 -d %s >/tmp/dhcp_eth1.log 2>&1 &", NET_ETH1);
		system_no_fd(cmd);

		// 等待DHCP响应
		Sleep(5000);

		// 重新检测
		eth0_detected = net_detect_network_segment(NET_ETH0, &eth0_info);
		eth1_detected = net_detect_network_segment(NET_ETH1, &eth1_info);
	}

	// 分析检测结果
	if (eth0_detected && eth1_detected) {
		// 两个接口都检测到网段信息，比较是否相同
		g_same_segment_detected = net_is_same_network_segment(&eth0_info, &eth1_info);
		g_network_segment_detected = TRUE;

		if (g_same_segment_detected) {
			LOGW("Same network segment detected - will only configure ETH0 to avoid IP conflicts");
			return NET_ST_ETH0;
		} else {
			LOGI("Different network segments detected - will configure both interfaces");
			return NET_ST_DUAL_ETH;
		}
	} else if (eth0_detected || eth1_detected) {
		// 只有一个接口检测到网段
		LOGI("Only one interface detected network segment: ETH0=%d, ETH1=%d",
			 eth0_detected, eth1_detected);
		g_network_segment_detected = TRUE;
		g_same_segment_detected = FALSE;
		return NET_ST_DUAL_ETH;  // 不同网段，可以同时配置
	}

	// 检测失败
	LOGW("Network segment detection failed for both interfaces");
	g_network_segment_detected = FALSE;
	return NET_ST_NONE;
}







/**
 * 双网口负载均衡检查（集成智能IP分配）
 * @return 返回当前最佳网络接口状态
 */
INT32 net_dual_eth_load_balance()
{
	if (!g_dual_eth_mode) {
		return net_if_ready(NET_ETH0, NULL) ? NET_ST_ETH0 : NET_ST_NONE;
	}

	// 检查物理连接状态
	UINT8 eth0_carrier = net_dev_carrier(NET_ETH0);
	UINT8 eth1_carrier = net_dev_carrier(NET_ETH1);
	UINT8 eth0_ready = net_if_ready(NET_ETH0, NULL);
	UINT8 eth1_ready = net_if_ready(NET_ETH1, NULL);

	// 如果启用了智能IP分配且两个网口都有物理连接
	if (g_smart_ip_enabled && eth0_carrier && eth1_carrier) {
		LOGI("Smart IP allocation enabled, detecting network segments...");

		INT32 smart_allocation = net_smart_ip_allocation_detect();

		switch (smart_allocation) {
			case NET_ST_ETH0:
				// 相同网段，只配置ETH0
				LOGI("Same network segment detected - configuring ETH0 only");
				if (eth1_ready) {
					// 如果ETH1已经配置了IP，需要清除以避免冲突
					LOGW("Clearing ETH1 configuration to avoid IP conflicts in same segment");
					net_clear_interface_config(NET_ETH1);
				}
				return eth0_ready ? NET_ST_ETH0 : NET_ST_NONE;

			case NET_ST_DUAL_ETH:
				// 不同网段，可以同时配置
				LOGI("Different network segments detected - configuring both interfaces");
				if (eth0_ready && eth1_ready) {
					return NET_ST_DUAL_ETH;
				}
				else if (eth0_ready) {
					return NET_ST_ETH0;
				}
				else if (eth1_ready) {
					return NET_ST_ETH1;
				}
				break;

			case NET_ST_NONE:
				// 检测失败，使用传统逻辑
				LOGW("Smart IP allocation detection failed, using traditional logic");
				break;
		}
	}

	// 传统的负载均衡逻辑
	if (eth0_ready && eth1_ready) {
		return NET_ST_DUAL_ETH;
	}
	else if (eth0_ready) {
		return NET_ST_ETH0;
	}
	else if (eth1_ready) {
		return NET_ST_ETH1;
	}

	return NET_ST_NONE;
}

/**
 * 清除网络接口配置
 * @param if_name 网络接口名称
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_clear_interface_config(LPCSTR if_name)
{
	if (!if_name || !net_dev_exist(if_name)) {
		return FALSE;
	}

	LOGI("Clearing network configuration for interface: %s", if_name);

	CHAR cmd[256];

	// 清除IP地址
	sprintf(cmd, "ip addr flush dev %s", if_name);
	system_run(cmd);

	// 清除路由
	sprintf(cmd, "ip route | grep \"%s\" | while read line; do ip route del $line; done", if_name);
	system_run(cmd);

	// 停止该接口的DHCP客户端
	sprintf(cmd, "killall -9 udhcpc; pkill -f \"udhcpc.*%s\"", if_name);
	system_run(cmd);

	// 将接口设置为down状态（可选）
	sprintf(cmd, "ifconfig %s 0.0.0.0", if_name);
	system_run(cmd);

	LOGI("Network configuration cleared for interface: %s", if_name);
	return TRUE;
}

/**
 * 网线断开时的接口配置清理（热插拔优化）
 * @param if_name 网络接口名称
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_cleanup_interface_on_disconnect(LPCSTR if_name)
{
	if (!if_name) {
		LOGE("Invalid interface name for disconnect cleanup");
		return FALSE;
	}

	// 检查物理连接状态，确认确实断开
	if (net_dev_carrier(if_name)) {
		LOGW("Interface %s still has carrier, skipping cleanup", if_name);
		return FALSE;
	}

	LOGI("Starting hot-unplug cleanup for interface: %s", if_name);

	CHAR cmd[512];
	UINT8 is_current_active = FALSE;
	CHAR current_ip[32] = {0};
	// 检查是否有其他活动接口
	UINT8 has_other_active = FALSE;

	// 检查是否是当前活动接口
	LPCSTR current_if = net_get_work_ifname();
	if (current_if && stricmp(current_if, if_name) == 0) {
		is_current_active = TRUE;
		net_if_ready(if_name, current_ip);
		LOGI("Cleaning up current active interface %s (IP: %s)", if_name,
			 strlen(current_ip) > 0 ? current_ip : "none");
	}

	// 1. 立即清除IP地址配置
	LOGI("Step 1: Flushing IP addresses for %s", if_name);
	sprintf(cmd, "ip addr flush dev %s 2>/dev/null", if_name);
	if (system_run(cmd) == 0) {
		LOGI("IP addresses flushed successfully for %s", if_name);
	} else {
		LOGW("Failed to flush IP addresses for %s", if_name);
	}

	// 2. 删除该接口相关的所有路由表项
	LOGI("Step 2: Removing routes for %s", if_name);
	sprintf(cmd, "ip route show | grep '%s' | while read route; do "
				 "echo \"Removing route: $route\"; "
				 "ip route del $route 2>/dev/null || true; done", if_name);
	system_run(cmd);

	// 3. 清除默认网关（如果通过该接口设置）
	sprintf(cmd, "ip route show default | grep '%s' | while read route; do "
				 "echo \"Removing default route: $route\"; "
				 "ip route del $route 2>/dev/null || true; done", if_name);
	system_run(cmd);

	// 4. 停止该接口的DHCP客户端进程
	LOGI("Step 3: Stopping DHCP client for %s", if_name);
	sprintf(cmd, "pkill -f \"udhcpc.*-i %s\" 2>/dev/null || true", if_name);
	system_run(cmd);

	// 额外确保清理
	sprintf(cmd, "ps aux | grep \"udhcpc.*%s\" | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null || true", if_name);
	system_run(cmd);

	// 5. 清除DNS配置（如果是当前活动接口）
	if (is_current_active) {
		LOGI("Step 4: Cleaning DNS configuration (was active interface)");

		// 备份当前DNS配置
		system_run("cp /tmp/resolv.conf /tmp/resolv.conf.backup 2>/dev/null || true");

		
		if (g_dual_eth_mode) {
			LPCSTR other_if = (stricmp(if_name, NET_ETH0) == 0) ? NET_ETH1 : NET_ETH0;
			if (net_if_ready(other_if, NULL)) {
				has_other_active = TRUE;
				LOGI("Other interface %s is still active, preserving DNS", other_if);
			}
		}

		// 如果没有其他活动接口，使用默认DNS
		if (!has_other_active) {
			FILE *resolv_file = fopen("/tmp/resolv.conf", "w");
			if (resolv_file) {
				fprintf(resolv_file, "# DNS cleared due to interface %s disconnect\n", if_name);
				fprintf(resolv_file, "nameserver ***************\n");
				fprintf(resolv_file, "nameserver *********\n");
				fprintf(resolv_file, "nameserver *******\n");
				fclose(resolv_file);
				LOGI("DNS configuration reset to default servers");
			}
		}
	}

	// 6. 清理内存中的网络配置状态
	UINT8 is_eth1 = (stricmp(if_name, NET_ETH1) == 0) ? TRUE : FALSE;
	T_SET_NETWORK *net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

	// 备份配置（保留DHCP设置和静态配置，但清除运行时状态）
	LOGI("Step 5: Clearing runtime network state for %s", if_name);

	// 重置调用计数器，为下次连接做准备
	if (stricmp(if_name, NET_ETH0) == 0) {
		g_eth0_load_config_count = 0;
	} else if (stricmp(if_name, NET_ETH1) == 0) {
		g_eth1_load_config_count = 0;
	}

	// 7. 将接口设置为初始状态
	LOGI("Step 6: Resetting interface %s to initial state", if_name);
	sprintf(cmd, "ifconfig %s 0.0.0.0 2>/dev/null || true", if_name);
	system_run(cmd);

	// 记录详细的清理日志
	LOGI("Hot-unplug cleanup completed for %s:", if_name);
	LOGI("  - IP addresses flushed");
	LOGI("  - Routes removed");
	LOGI("  - DHCP client stopped");
	LOGI("  - Interface reset to initial state");
	if (is_current_active) {
		LOGI("  - DNS configuration %s", has_other_active ? "preserved" : "reset to defaults");
	}
	LOGI("Interface %s is ready for next hot-plug event", if_name);

	return TRUE;
}

/**
 * 检查接口是否准备好进行热插拔恢复配置
 * @param if_name 网络接口名称
 * @return 准备好返回TRUE，否则返回FALSE
 */
UINT8 net_is_interface_ready_for_hotplug_recovery(LPCSTR if_name)
{
	if (!if_name || !net_dev_exist(if_name)) {
		return FALSE;
	}

	// 检查物理连接状态
	if (!net_dev_carrier(if_name)) {
		LOGE("Interface %s not ready: no carrier", if_name);
		return FALSE;
	}

	// 检查是否已经有IP配置
	CHAR current_ip[32];
	if (net_if_ready(if_name, current_ip) && strlen(current_ip) > 0) {
		LOGI("Interface %s already configured with IP: %s", if_name, current_ip);
		return FALSE; // 已经配置，不需要重新配置
	}

	LOGI("Interface %s is ready for hot-plug recovery configuration", if_name);
	return TRUE;
}

/**
 * 网线热插拔恢复处理
 * @param if_name 网络接口名称
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_handle_hotplug_recovery(LPCSTR if_name)
{
	if (!net_is_interface_ready_for_hotplug_recovery(if_name)) {
		return FALSE;
	}

	LOGI("Starting hot-plug recovery for interface: %s", if_name);

	// 等待物理连接稳定
	Sleep(2000);

	// 再次确认物理连接状态
	if (!net_dev_carrier(if_name)) {
		LOGW("Interface %s lost carrier during recovery delay", if_name);
		return FALSE;
	}

	// 使用自动配置功能
	if (g_auto_ip_config_enabled) {
		LOGI("Using auto IP configuration for hot-plug recovery of %s", if_name);
		if (net_configure_single_interface(if_name)) {
			LOGI("Hot-plug recovery successful for %s", if_name);

			// 更新网络状态
			if (stricmp(if_name, NET_ETH0) == 0) {
				if (g_dual_eth_mode && net_if_ready(NET_ETH1, NULL)) {
					g_if_save_state = NET_ST_DUAL_ETH;
				} else {
					g_if_save_state = NET_ST_ETH0;
				}
			} else if (stricmp(if_name, NET_ETH1) == 0) {
				if (net_if_ready(NET_ETH0, NULL)) {
					g_if_save_state = NET_ST_DUAL_ETH;
				} else {
					g_if_save_state = NET_ST_ETH1;
				}
			}

			// 同步时间
			sync_time(TIME_CLOCK_SVR, FALSE);
			return TRUE;
		} else {
			LOGE("Auto IP configuration failed for hot-plug recovery of %s", if_name);
		}
	}

	// 回退到传统配置方法
	LOGI("Using traditional configuration for hot-plug recovery of %s", if_name);
	INT32 result = net_load_config(if_name);
	if (result == OK) {
		LOGI("Traditional configuration successful for hot-plug recovery of %s", if_name);
		return TRUE;
	} else {
		LOGE("Traditional configuration failed for hot-plug recovery of %s", if_name);
		return FALSE;
	}
}

/**
 * 网络故障转移处理
 * @param current_if 当前使用的接口
 * @return 返回建议使用的接口，NULL表示无可用接口
 */
LPCSTR net_failover_handler(LPCSTR current_if)
{
	if (!g_dual_eth_mode) {
		return net_if_ready(NET_ETH0, NULL) ? NET_ETH0 : NULL;
	}

	UINT32 current_time = get_app_uptime();
	UINT8 eth0_ready = net_if_ready(NET_ETH0, NULL);
	UINT8 eth1_ready = net_if_ready(NET_ETH1, NULL);

	// 更新故障计数
	if (!eth0_ready) {
		g_eth0_fail_count++;
	} else {
		g_eth0_fail_count = 0;
	}

	if (!eth1_ready) {
		g_eth1_fail_count++;
	} else {
		g_eth1_fail_count = 0;
	}

	// 防止频繁切换 - 至少间隔5秒
	if (current_time - g_last_failover_time < 5) {
		if (current_if && net_if_ready(current_if, NULL)) {
			return current_if;
		}
	}

	// 故障转移逻辑
	if (current_if && stricmp(current_if, NET_ETH0) == 0) {
		// 当前使用eth0
		if (!eth0_ready && eth1_ready && g_eth0_fail_count >= 3) {
			LOGW("Failover from eth0 to eth1 (eth0 failed %d times)", g_eth0_fail_count);
			g_last_failover_time = current_time;
			g_primary_interface = NET_ETH1;
			return NET_ETH1;
		}
	} else if (current_if && stricmp(current_if, NET_ETH1) == 0) {
		// 当前使用eth1
		if (!eth1_ready && eth0_ready && g_eth1_fail_count >= 3) {
			LOGW("Failover from eth1 to eth0 (eth1 failed %d times)", g_eth1_fail_count);
			g_last_failover_time = current_time;
			g_primary_interface = NET_ETH0;
			return NET_ETH0;
		}
	}

	// 自动恢复到主接口
	if (stricmp(g_primary_interface, NET_ETH0) == 0 && eth0_ready &&
		current_if && stricmp(current_if, NET_ETH1) == 0 &&
		current_time - g_last_failover_time > 30) {
		LOGI("Auto recovery to primary interface eth0");
		g_last_failover_time = current_time;
		return NET_ETH0;
	} else if (stricmp(g_primary_interface, NET_ETH1) == 0 && eth1_ready &&
			   current_if && stricmp(current_if, NET_ETH0) == 0 &&
			   current_time - g_last_failover_time > 30) {
		LOGI("Auto recovery to primary interface eth1");
		g_last_failover_time = current_time;
		return NET_ETH1;
	}

	// 返回当前接口或简单的接口选择
	if (current_if && net_if_ready(current_if, NULL)) {
		return current_if;
	}

	// 基于物理连接的简单接口选择
	UINT8 eth0_carrier = net_dev_carrier(NET_ETH0);
	UINT8 eth1_carrier = g_dual_eth_mode ? net_dev_carrier(NET_ETH1) : FALSE;

	// 优先选择有物理连接且已配置的接口
	if (eth0_carrier && net_if_ready(NET_ETH0, NULL)) {
		return NET_ETH0;
	} else if (eth1_carrier && net_if_ready(NET_ETH1, NULL)) {
		return NET_ETH1;
	}

	// 如果都没有配置，选择有物理连接的接口
	if (eth0_carrier) {
		return NET_ETH0;
	} else if (eth1_carrier) {
		return NET_ETH1;
	}

	return NULL;
}

/**
 * 获取当前活动的网络接口信息
 * @param eth0_status 返回eth0状态 (可为NULL)
 * @param eth1_status 返回eth1状态 (可为NULL)
 * @return 当前网络状态
 */
INT32 net_get_active_interfaces(UINT8 *eth0_status, UINT8 *eth1_status)
{
	UINT8 eth0_ready = net_if_ready(NET_ETH0, NULL);
	UINT8 eth1_ready = g_dual_eth_mode ? net_if_ready(NET_ETH1, NULL) : FALSE;

	if (eth0_status) *eth0_status = eth0_ready;
	if (eth1_status) *eth1_status = eth1_ready;

	if (eth0_ready && eth1_ready) {
		return NET_ST_DUAL_ETH;
	}
	else if (eth0_ready) {
		return NET_ST_ETH0;
	}
	else if (eth1_ready) {
		return NET_ST_ETH1;
	}

	return NET_ST_NONE;
}










/**
 * 获取双网口模式状态信息
 * @param status_info 返回状态信息的JSON字符串指针
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_get_dual_eth_status(CHAR **status_info)
{
	if (!status_info) return FALSE;

	cJSON *root = cJSON_CreateObject();
	if (!root) return FALSE;

	// 基本信息
	cJSON_AddBoolToObject(root, "dual_eth_mode", g_dual_eth_mode);
	cJSON_AddBoolToObject(root, "eth1_priority", g_eth1_priority);
	cJSON_AddNumberToObject(root, "current_state", g_if_save_state);
	cJSON_AddStringToObject(root, "current_interface", net_get_work_ifname());

	// ETH0状态
	cJSON *eth0_obj = cJSON_CreateObject();
	UINT8 eth0_ready = net_if_ready(NET_ETH0, NULL);
	cJSON_AddBoolToObject(eth0_obj, "ready", eth0_ready);
	cJSON_AddBoolToObject(eth0_obj, "carrier", net_dev_carrier(NET_ETH0));
	cJSON_AddNumberToObject(eth0_obj, "fail_count", g_eth0_fail_count);
	cJSON_AddItemToObject(root, "eth0", eth0_obj);

	// ETH1状态
	cJSON *eth1_obj = cJSON_CreateObject();
	UINT8 eth1_ready = net_if_ready(NET_ETH1, NULL);
	cJSON_AddBoolToObject(eth1_obj, "ready", eth1_ready);
	cJSON_AddBoolToObject(eth1_obj, "carrier", net_dev_carrier(NET_ETH1));
	cJSON_AddNumberToObject(eth1_obj, "fail_count", g_eth1_fail_count);

	cJSON_AddItemToObject(root, "eth1", eth1_obj);

	// 故障转移信息
	cJSON *failover_obj = cJSON_CreateObject();
	cJSON_AddNumberToObject(failover_obj, "last_failover_time", g_last_failover_time);
	cJSON_AddStringToObject(failover_obj, "primary_interface", g_primary_interface);

	// 简化的接口建议逻辑
	LPCSTR suggested_if = NULL;
	if (net_if_ready(NET_ETH0, NULL)) {
		suggested_if = NET_ETH0;
	} else if (g_dual_eth_mode && net_if_ready(NET_ETH1, NULL)) {
		suggested_if = NET_ETH1;
	}
	cJSON_AddStringToObject(failover_obj, "suggested_interface", suggested_if ? suggested_if : "none");
	cJSON_AddItemToObject(root, "failover", failover_obj);

	*status_info = cJSON_PrintUnformatted(root);
	cJSON_Delete(root);

	return *status_info != NULL;
}

/**
 * 设置网络接口优先级
 * @param primary_if 主接口名称
 * @param secondary_if 备用接口名称
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_set_interface_priority(LPCSTR primary_if, LPCSTR secondary_if)
{
	if (!primary_if) return FALSE;

	if (stricmp(primary_if, NET_ETH1) == 0) {
		g_eth1_priority = TRUE;
		g_primary_interface = NET_ETH1;
		LOGI("Set ETH1 as primary interface");
	} else if (stricmp(primary_if, NET_ETH0) == 0) {
		g_eth1_priority = FALSE;
		g_primary_interface = NET_ETH0;
		LOGI("Set ETH0 as primary interface");
	} else {
		LOGE("Invalid primary interface: %s", primary_if);
		return FALSE;
	}

	// 如果当前不是主接口且主接口可用，则切换
	LPCSTR current_if = net_get_work_ifname();
	if (stricmp(current_if, primary_if) != 0 && net_if_ready(primary_if, NULL)) {
		LOGI("Switching to primary interface: %s", primary_if);
		net_load_config(primary_if);
	}

	return TRUE;
}

/**
 * 强制切换到指定网络接口
 * @param if_name 目标接口名称
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_force_switch_interface(LPCSTR if_name)
{
	if (!if_name || !net_dev_exist(if_name)) {
		LOGE("Invalid or non-existent interface: %s", if_name ? if_name : "NULL");
		return FALSE;
	}

	LOGI("Force switching to interface: %s", if_name);

	// 更新当前状态
	if (stricmp(if_name, NET_ETH0) == 0) {
		g_if_save_state = NET_ST_ETH0;
	} else if (stricmp(if_name, NET_ETH1) == 0) {
		g_if_save_state = NET_ST_ETH1;
	}

	// 加载网络配置
	INT32 result = net_load_config(if_name);
	if (result == OK) {
		LOGI("Successfully switched to interface: %s", if_name);
		g_last_failover_time = get_app_uptime();
		return TRUE;
	} else {
		LOGE("Failed to switch to interface: %s", if_name);
		return FALSE;
	}
}


LPVOID logs_thread(LPVOID arg)
{	
	THREAD_FUNC_BEGIN();

	INT32	sock, new_fd;	
    INT32 	fnRet, flag = 1;
	struct sockaddr_in 	svr_addr;
	struct sockaddr_in 	client_addr;
	struct timeval  	tv;
	fd_set				read_fds;
	socklen_t			skt_len;
	INT32				org_fd;

	g_logs_opened = TRUE;
	sock = socket(AF_INET, SOCK_STREAM, 0);
    if (setsockopt(sock, SOL_SOCKET, SO_REUSEADDR, &flag, sizeof(flag)) < 0) {
		LOGE("setsockopt(SO_REUSEADDR) error=%d", errno);
		goto FN_END;
	}
	
    QfSet0(&svr_addr, sizeof(svr_addr));
    svr_addr.sin_family = AF_INET;
    svr_addr.sin_addr.s_addr = htons(INADDR_ANY);
    svr_addr.sin_port = htons(3001);
	//set_socket_noblock(sock, O_NONBLOCK);
    if (bind(sock, (struct sockaddr *)&svr_addr, sizeof(svr_addr)) < 0) {
		LOGE("bind() error=%d", errno);
		goto FN_END;
	}
	
    if (listen(sock, 2) < 0) {
		LOGE("listen() error=%d", errno);
		goto FN_END;
	}

	org_fd = dup(STDOUT_FILENO);
	while (!system_quit() && g_logs_opened) {		
		
		FD_ZERO(&read_fds);					
        FD_SET(sock, &read_fds);
        tv.tv_sec  = 2;
        tv.tv_usec = 0;		
		fnRet = ::select(sock+1, &read_fds, NULL, NULL, &tv);//检测socket是否有效
		if (fnRet < 0) 
			break;

		if (fnRet EQU 0 || 
			!FD_ISSET(sock, &read_fds))
			continue;

		skt_len = sizeof(client_addr);
        if ((new_fd = accept(sock, (sockaddr*)&client_addr, &skt_len)) == -1) {   
            LOGE_NF("Accept error:%s\n\a", strerror(errno));   
            continue;   
        }  
		LOGI_NF("new client, %s:%d", inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port));
		
		dup2(new_fd, STDOUT_FILENO);
        close(new_fd);
		
	}
	dup2(org_fd, STDOUT_FILENO);

FN_END:
	close(sock);

	THREAD_FUNC_END();
}


/**
 * 内网搜索json命令
 */
VOID net_srch_json(LPCSTR recv_msg)
{
	cJSON 	*root;	
	
	root = cJSON_Parse(recv_msg);
	if (root) {
		do {
			INT32	fnRet;
			cJSON 	*val_item = NULL;
			cJSON 	*item = cJSON_GetObjectItem(root, "cmd");

			if (strcmp(item->valuestring, "update") EQU 0) 		// 升级
			{
				val_item = cJSON_GetObjectItem(root, "model");
				if (val_item EQU NULL || strcmp(val_item->valuestring, g_pEmbedInfo->model) != 0) {
					LOGI_NF("%s() cmd:%s model mismatch", __func__, val_item->valuestring);
					break;
				}
				val_item = cJSON_GetObjectItem(root, "val");
				if (val_item) 
					system_upgrade(SYS_UP_URL, val_item->valuestring);
				else 
					system_upgrade(SYS_UP_AUTO, NULL);
			}
			else if (strcmp(item->valuestring, "reboot") EQU 0)	// 重启
            {
	            val_item = cJSON_GetObjectItem(root, "model");
          		if (val_item EQU NULL || strcmp(val_item->valuestring, g_pEmbedInfo->model) != 0) {
					LOGI_NF("%s() cmd:%s model mismatch", __func__, val_item->valuestring);
					break;
				}
               	fnRet = system_reboot();
            }
            else if (strcmp(item->valuestring, "reset") EQU 0)	// 恢复出厂设置
            {
            	val_item = cJSON_GetObjectItem(root, "model");
	            if (val_item EQU NULL || strcmp(val_item->valuestring, g_pEmbedInfo->model) != 0) {
					LOGI_NF("%s() cmd:%s model mismatch", __func__, val_item->valuestring);
					break;
				}
            	deltree_dir(CFG_PATH);
				Sleep(3000);
				fnRet = system_reboot();
            }
			else if (strcmp(item->valuestring, "burn-in") EQU 0)	// 老化
            {
            	val_item = cJSON_GetObjectItem(root, "model");
	            if (val_item EQU NULL || strcmp(val_item->valuestring, g_pEmbedInfo->model) != 0) {
					LOGI_NF("%s() cmd:%s model mismatch", __func__, val_item->valuestring);
					break;
				}
				if (factory_is_run()) {
					LOGE_NF("%s() factory has run", __func__);
					break;
				}
				factory_fuc(FFUNC_BURN_IN, NULL, (LPVOID)-1);
				
            }
			else if (strcmp(item->valuestring, "logsvr") EQU 0)	// 开启日志服务
            {
               	val_item = cJSON_GetObjectItem(root, "val");
				if (val_item) 
					sys_log_upload(val_item->valuestring);
            }
			else if (strcmp(item->valuestring, "netlog") EQU 0)	// 开启网络日志
            {
            	val_item = cJSON_GetObjectItem(root, "val");
				if (val_item && val_item->valueint EQU 1) {					
	            	if (!g_logs_opened) {
						g_logs_opened = TRUE;
						if (PTHREAD_CREATE(NULL, NULL, logs_thread, NULL)) {						
							g_logs_opened = FALSE;
							LOGE_NF("PTHREAD_CREATE(logs_thread), errno=%d", errno);
						}
					}
				} else{
					g_logs_opened = FALSE;
				}
            }							
			else if (strcmp(item->valuestring, "telnet") EQU 0)	// telnet
            {
				system_no_fd("/usr/sbin/telnetd&");
            }
			else if (strcmp(item->valuestring, "vssocket") EQU 0)	// vssocket
            {
				extern UINT8 vssocket_open();

				// vssocket_open();
            }
			else if (strcmp(item->valuestring, "web") EQU 0)	// web
            {
				system_no_fd("/opt/web/websvr -c /opt/web&");
			}
			else if (strcmp(item->valuestring, "ftp") EQU 0)	// ftp
            {
				system_no_fd("export TZ=UTC+00:00;tcpsvd 0 21 ftpd /mnt/sdcard&");
            }
			else if (strcmp(item->valuestring, "ip_config") EQU 0)	// 配置IP地址
            {
            	LOGI_NF("%s(ip_config) doc: %s", __func__, recv_msg);
            	val_item = cJSON_GetObjectItem(root, "dev");
				if (val_item && net_dev_exist(val_item->valuestring)) 
				{
					INT32 fd_tmp = open(TMP_NET_CFG, O_WRONLY|O_CREAT);

					if (fd_tmp) {
						UINT8	is_eth1 = stricmp(val_item->valuestring, NET_ETH1) EQU 0 ? TRUE:FALSE;
						T_SET_NETWORK 	*net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

						write(fd_tmp, recv_msg, strlen(recv_msg)+1);
						close(fd_tmp);
						if (settings_load_network(TMP_NET_CFG, net)) {
							net_load_config(is_eth1 ? NET_ETH1 : NET_ETH0);
							settings_save_net(is_eth1 ? NET_ETH1 : NET_ETH0);
						}
					}
				}
            }
			else if (strcmp(item->valuestring, "ip_config_bc") EQU 0)	// 广播方式配置IP地址
            {
            	val_item = cJSON_GetObjectItem(root, "mac");
          		if (val_item EQU NULL || strcmp(val_item->valuestring, get_platform_mac()) != 0) {
					LOGI_NF("%s(ip_config_bc) mac:%s mismatch, dev_mac:%s", __func__, val_item->valuestring, get_platform_mac());
					break;
				}
				LOGI_NF("%s(ip_config_bc) doc: %s", __func__, recv_msg);

            	val_item = cJSON_GetObjectItem(root, "dev");
				if (val_item && net_dev_exist(val_item->valuestring))
				{
	            	INT32 fd_tmp = open(TMP_NET_CFG, O_WRONLY|O_CREAT);

					if (fd_tmp) {
						UINT8	is_eth1 = stricmp(val_item->valuestring, NET_ETH1) EQU 0 ? TRUE:FALSE;
						T_SET_NETWORK 	*net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

						write(fd_tmp, recv_msg, strlen(recv_msg)+1);
						close(fd_tmp);
						if (settings_load_network(TMP_NET_CFG, net)) {
							net_load_config(is_eth1 ? NET_ETH1 : NET_ETH0);
							settings_save_net(is_eth1 ? NET_ETH1 : NET_ETH0);
						}
					}
				}
            }
			else if (strcmp(item->valuestring, "auto_ip_config") EQU 0)	// 启动时自动IP配置
            {
            	LOGI_NF("%s(auto_ip_config) doc: %s", __func__, recv_msg);

            	// 启用/禁用启动时自动IP配置
            	val_item = cJSON_GetObjectItem(root, "enabled");
				if (val_item) {
					net_set_auto_ip_config_enabled(val_item->valueint ? TRUE : FALSE);
					LOGI("Auto IP configuration %s", val_item->valueint ? "enabled" : "disabled");
				}

				// 强制执行启动配置
				val_item = cJSON_GetObjectItem(root, "force_startup_config");
				if (val_item && val_item->valueint) {
					LOGI("Forcing startup auto IP configuration...");
					g_startup_config_completed = FALSE;  // 重置完成标志

					// 在单独线程中执行
					pthread_t config_thread;
					if (PTHREAD_CREATE(&config_thread, NULL, net_startup_config_thread, NULL) == 0) {
						pthread_detach(config_thread);
						LOGI("Startup auto IP configuration thread created");
					} else {
						// 如果线程创建失败，直接执行
						net_startup_auto_ip_config();
					}
				}

				// 执行单个接口的配置
				val_item = cJSON_GetObjectItem(root, "hotplug_interface");
				if (val_item && val_item->valuestring) {
					LOGI("Triggering interface configuration for %s", val_item->valuestring);
					if (net_load_config(val_item->valuestring) == OK) {
						LOGI("Interface configuration successful for %s", val_item->valuestring);
					} else {
						LOGE("Interface configuration failed for %s", val_item->valuestring);
					}
				}

				// 获取自动配置状态
				val_item = cJSON_GetObjectItem(root, "get_status");
				if (val_item && val_item->valueint) {
					cJSON *status = cJSON_CreateObject();
					cJSON_AddBoolToObject(status, "auto_ip_enabled", g_auto_ip_config_enabled);
					cJSON_AddBoolToObject(status, "startup_completed", g_startup_config_completed);
					cJSON_AddNumberToObject(status, "startup_time", g_startup_config_time);

					// 接口状态
					cJSON *interfaces = cJSON_CreateObject();

					// ETH0状态
					cJSON *eth0_status = cJSON_CreateObject();
					cJSON_AddBoolToObject(eth0_status, "exists", net_dev_exist(NET_ETH0));
					cJSON_AddBoolToObject(eth0_status, "carrier", net_dev_carrier(NET_ETH0));
					cJSON_AddBoolToObject(eth0_status, "ready", net_if_ready(NET_ETH0, NULL));

					CHAR config_file[128];
					if (net_find_config_file(NET_ETH0, config_file)) {
						cJSON_AddStringToObject(eth0_status, "config_file", config_file);
					}
					cJSON_AddItemToObject(interfaces, "eth0", eth0_status);

					// ETH1状态
					cJSON *eth1_status = cJSON_CreateObject();
					cJSON_AddBoolToObject(eth1_status, "exists", net_dev_exist(NET_ETH1));
					cJSON_AddBoolToObject(eth1_status, "carrier", net_dev_carrier(NET_ETH1));
					cJSON_AddBoolToObject(eth1_status, "ready", net_if_ready(NET_ETH1, NULL));

					if (net_find_config_file(NET_ETH1, config_file)) {
						cJSON_AddStringToObject(eth1_status, "config_file", config_file);
					}
					cJSON_AddItemToObject(interfaces, "eth1", eth1_status);

					cJSON_AddItemToObject(status, "interfaces", interfaces);

					CHAR *status_str = cJSON_PrintUnformatted(status);
					if (status_str) {
						LOGI_NF("Auto IP config status: %s", status_str);
						free(status_str);
					}
					cJSON_Delete(status);
				}
            }
			else if (strcmp(item->valuestring, "smart_ip_config") EQU 0)	// 智能IP分配配置
            {
            	LOGI_NF("%s(smart_ip_config) doc: %s", __func__, recv_msg);

            	// 启用/禁用智能IP分配
            	val_item = cJSON_GetObjectItem(root, "enabled");
				if (val_item) {
					net_set_smart_ip_enabled(val_item->valueint ? TRUE : FALSE);
					LOGI("Smart IP allocation %s", val_item->valueint ? "enabled" : "disabled");
				}

				// 强制重新检测网段
				val_item = cJSON_GetObjectItem(root, "force_detect");
				if (val_item && val_item->valueint) {
					LOGI("Forcing network segment detection...");
					g_network_segment_detected = FALSE;
					g_last_segment_check_time = 0;

					// 触发重新检测
					if (g_dual_eth_mode) {
						INT32 detection_result = net_smart_ip_allocation_detect();
						LOGI("Forced detection result: %d", detection_result);
					}
				}

				// 获取状态信息
				val_item = cJSON_GetObjectItem(root, "get_status");
				if (val_item && val_item->valueint) {
					CHAR *status_info = NULL;
					if (net_get_smart_ip_status(&status_info)) {
						LOGI_NF("Smart IP status: %s", status_info);
						free(status_info);
					}
				}
            }
				
		} while (FALSE);
		
	}
	else {
		LOGE("%s() err body: %s", __func__, recv_msg);
	}
	cJSON_Delete(root);
}


/**
 * 内网搜索线程
 */
LPVOID net_srch_cmd_thread(LPVOID arg)
{	
	SET_THREAD_NAME();

	INT32		scktFd = (INT32)(intptr_t)arg;
	socklen_t	iSize = sizeof(sockaddr_in);
	sockaddr_in	rmtAddr;	
	fd_set		read_fds;
	timeval 	tv;	
	INT32		fnRet;	
	BYTE		*data = (BYTE *)malloc(MAX_NET_BUF); 
	PTR_MEM_BUF	writer = membuf_open(data, MAX_NET_BUF);
	PTR_MEM_BUF	reader = NULL;

	while (!system_quit()) {		

		FD_ZERO(&read_fds);  
		FD_SET(scktFd, &read_fds);
		tv.tv_sec 	= 2; // 2s超时
		tv.tv_usec 	= 0; 		
		fnRet = ::select(scktFd+1, &read_fds, NULL, NULL, &tv);//检测socket是否有效
		if (fnRet < 0) 
			break;

		if (fnRet EQU 0 || 
			!FD_ISSET(scktFd, &read_fds))
			continue;
		
		// 接收数据	
		{
			fnRet = recvfrom(scktFd, data, MAX_NET_BUF, 0, (struct sockaddr *)&rmtAddr, &iSize);
 			if (fnRet <= 0)  
 				continue;

//			LOGI("recvfrom(%s:%d, len=%d)", inet_ntoa(rmtAddr.sin_addr), ntohs(rmtAddr.sin_port), fnRet);
			
			{
				SEARCH_CMD	*ptPackHead;

				membuf_close(&reader);
				reader = membuf_open(data, fnRet);
				membuf_rewind(writer);

				ptPackHead = (SEARCH_CMD *)membuf_read(reader, sizeof(SEARCH_CMD));
				if (ptPackHead EQU NULL ||
					ptPackHead->dwFlag != HEAD_FLAG)
					continue;

				switch (ptPackHead->dwCmd)
				{
				case CMD_GET_DS_INFO: {	// 获取设备信息
					P2P_DEVICE_INFO_EX	*ptMsgDataEx;
					CHAR	ver_buf[200];
					UINT32 	len = DIE_V2;

					ptMsgDataEx = (P2P_DEVICE_INFO_EX *)membuf_embezzle(writer, sizeof(P2P_DEVICE_INFO_EX));
					bzero(ptMsgDataEx, len);
					ptMsgDataEx->dwPackFlag = SERVER_PACK_FLAG;
					ptMsgDataEx->dwSize		= len;
					ptMsgDataEx->p2pDeviceInfo.devType		= g_pEmbedInfo->dev_type;
					ptMsgDataEx->p2pDeviceInfo.panoramaMode = g_pEmbedInfo->panorama_mode;
					ptMsgDataEx->p2pDeviceInfo.netFamily 	= DEV_FLAG_FAMILY;
					ptMsgDataEx->p2pDeviceInfo.language		= g_pEmbedInfo->language;
					ptMsgDataEx->p2pDeviceInfo.odmID		= g_pEmbedInfo->odm;
					ptMsgDataEx->p2pDeviceInfo.web_port		= VS_PORT_WEB;

#if defined(GB28181_ENABLE) && (GB_PLATFORM_ID == GB_PFID_SCDX_MJ)	// 四川电信魔镜		
				#if(GB28181_VERSION==0) 
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.hid, 		gb_get_devid());
				#else
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.hid, 		gbv2_get_devid());
				#endif
#else
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.hid, 		g_pEmbedInfo->hid);
#endif
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.p2pid, 		g_pEmbedInfo->p2pid);
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.model, 		g_pEmbedInfo->model);
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.version, 	vs_get_version(ver_buf));
					memcpy(ptMsgDataEx->p2pDeviceInfo.my_key, g_pEmbedInfo->my_key, sizeof(g_pEmbedInfo->my_key));
#if defined(VS_THRD_LICENSE)					
					ptMsgDataEx->p2pDeviceInfo.ai_lic = g_pEmbedInfo->thrd_key_len ? 1 : 0;
#endif
					sscanf(get_platform_mac(), 
						PF_MAC_FORMAT_HHU,
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[0],
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[1],
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[2],
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[3],
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[4],
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[5]);
//					LOGI_NF("platform_mac(%s), parse: " PF_MAC_FORMAT_HHU, get_platform_mac(),
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[0],
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[1],
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[2],
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[3],
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[4],
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[5]);
					{
						cJSON	*root = cJSON_CreateObject();

						{
							char chipsn[64];
							
							// 芯片序列号
							QfSet0(chipsn, sizeof(chipsn));
							vsipc_get_ml_sn(chipsn, sizeof(chipsn)-1);
							cJSON_AddStringToObject(root, "chipsn", chipsn);
						}
						cJSON_AddStringToObject(root, "hid", g_pEmbedInfo->hid);
						LPCSTR lte_info = file_exist(JS_LTE_STATE) ? JS_LTE_STATE : JS_LTE_INFO;
						jparse_value_fun(lte_info, "$.body", [&](cJSON *item){
							if (cJSON_IsObject(item)){
								cJSON *sub_item = cJSON_GetObjectItem(item, "imei");
								
								if (sub_item && cJSON_IsString(sub_item)) 
									cJSON_AddStringToObject(root, "imei", sub_item->valuestring);
								
								sub_item = cJSON_GetObjectItem(item, "ccid");
								if (sub_item && cJSON_IsString(sub_item)) 
									cJSON_AddStringToObject(root, "ccid", sub_item->valuestring);
							}
						});	

						{
							//增加网络配置信息
							settings_load_network(CFG_ETH("/tmp/"), &g_pRunSets->eth0);
							settings_save_net(NET_ETH0);
							
							cJSON *net_info = cJSON_CreateObject();
							cJSON *net_cfg = cJSON_Load(CFG_ETH(CFG_PATH));
							if(net_cfg != NULL)
							{
								cJSON *item=NULL;
								item = cJSON_GetObjectItem(net_cfg, "ip");
								if(item != NULL)
									cJSON_AddStringToObject(net_info, "ip",item->valuestring);

								item = cJSON_GetObjectItem(net_cfg, "dhcp");
								if(item != NULL)
									cJSON_AddNumberToObject(net_info, "dhcp",item->valueint);

								item = cJSON_GetObjectItem(net_cfg, "netmask");
								if(item != NULL)
									cJSON_AddStringToObject(net_info, "netmask",item->valuestring);

								item = cJSON_GetObjectItem(net_cfg, "gateway");
								if(item != NULL)
									cJSON_AddStringToObject(net_info, "gateway",item->valuestring);
								
								item = cJSON_GetObjectItem(net_cfg, "dns");
								if(item != NULL)
									cJSON_AddStringToObject(net_info, "dns",item->valuestring);
				
								cJSON_Delete(net_cfg);
							}

							cJSON_AddItemToObject(root, "network", net_info);					
						}

						char *out = cJSON_PrintUnformatted(root);
						membuf_write(writer, out, strlen(out)+1);
						free(out);
						cJSON_Delete(root);
					}
				}
				break;				

				case CMD_BATCH_SET_DS_INFO: 	// 批量设置, 除P2P_DEVICE_INFO中的p2pid其它的参数生效
				case CMD_SET_DS_INFO: {	// 设置设备信息
				
					P2P_DEVICE_INFO	*ptMsgData = (P2P_DEVICE_INFO *)membuf_get_current(reader);

					if (ptMsgData EQU NULL) {
						LOGE("CMD_SET_DS_INFO error 1");
						continue;
					}					
						
					g_pEmbedInfo->dev_type 		= ptMsgData->devType;
					g_pEmbedInfo->panorama_mode	= ptMsgData->panoramaMode;
					g_pEmbedInfo->language		= ptMsgData->language;
					g_pEmbedInfo->odm			= ptMsgData->odmID;
					strcpy(g_pEmbedInfo->model, ptMsgData->model);

					// 批量时不能改p2p号
					if (ptPackHead->dwCmd != CMD_BATCH_SET_DS_INFO) {
						strcpy(g_pEmbedInfo->p2pid, ptMsgData->p2pid);
					}

					// 全景或者带有扩展数据的
					if (sizeof(P2P_DEVICE_INFO) EQU membuf_remain_size(reader)) {
						memcpy(g_pEmbedInfo->my_key, ptMsgData->my_key, QfLength(g_pEmbedInfo->my_key));
					}
					
					if (save_embed_info(g_pEmbedInfo, NULL)) {
						LOGI("save_embed_info() succ");
					} else {
						LOGE("save_embed_info() fail");
					}
				}
				break;

				// WiFi functionality removed - dual Ethernet mode only

				case CMD_LICENSE:				// 普通授权模式
				case CMD_LICENSE_FORCE:{		// 普通授权模式
					if (strncmp((char*)(membuf_get_current(reader)), VS_LICENSE_FLAG, strlen(VS_LICENSE_FLAG)) EQU 0) {
						UINT32 	lic_type = ptPackHead->dwCmd - CMD_LICENSE + 1;
						CHAR	lic_ip[64];
						LPCSTR	svr_ip = (LPCSTR)membuf_get_current(reader)+strlen(VS_LICENSE_FLAG)+1;

						if (!IS_VALID_ID(g_pEmbedInfo->p2pid))
							lic_type = 1;

						QfSet0(lic_ip, sizeof(lic_ip));
						if (isdigit(svr_ip[0])) {
							s_strcpy(lic_ip, svr_ip);
						}	
						else {
							s_strcpy(lic_ip, inet_ntoa(rmtAddr.sin_addr));
						}
						factory_fuc(FFUNC_LICENSE, (LPVOID)lic_type, lic_ip);
					}
					else {
						LOGE("CMD_LICENSE(code=%u) from_ip=%s", ptPackHead->dwCmd, inet_ntoa(rmtAddr.sin_addr));
					}
				}
				break;

				case CMD_JSON_REQUET: {	// json请求
					net_srch_json((LPCSTR)(membuf_get_current(reader)));
				}
				break;
				
				}

				if (membuf_get_size(writer) > 0) {
					
					sockaddr_in toAddr;
								
					toAddr.sin_family 		= AF_INET;
					toAddr.sin_port 		= htons(VS_PORT_SRCH_DATA);
					//toAddr.sin_addr.s_addr = inet_addr(getBCAddr().c_str());	// 只能在广播域网才可以接收到数据
					toAddr.sin_addr.s_addr 	= inet_addr(QFPH_MULTI_ADDR);
					fnRet = sendto(scktFd, membuf_get_pointer(writer), membuf_get_size(writer), 0, (struct sockaddr *)&toAddr, sizeof(toAddr));
					if (fnRet < 0) {
						LOGE("sendto(%s:%d, len=%d)=%d, err=%d", inet_ntoa(toAddr.sin_addr), ntohs(toAddr.sin_port), membuf_get_size(writer), fnRet, errno);
					}

					// 避免部分安卓机无法收到数据
					fnRet = sendto(scktFd, membuf_get_pointer(writer), membuf_get_size(writer), 0, (struct sockaddr *)&rmtAddr, sizeof(rmtAddr));
//					LOGI("sendto(%s:%d, len=%d)=%d", inet_ntoa(rmtAddr.sin_addr), ntohs(rmtAddr.sin_port), membuf_get_size(writer), fnRet);
				}

			}
		}
	}
	close(scktFd);
	free(data);
	membuf_close(&reader);
	membuf_close(&writer);

	END_THREAD;
	
	return NULL;
}


/**
 * 关于网络状态的改变,
 *	1.有线网络检测非常准确,也很真实,上线与下线比较容易区分,所以检测到网络掉线后,
 		可以直接切换到wifi模式
 *	2.wifi的切换是在有线断开的前提下,或是一开始就没有插有线的情况下
 		无线的切换需要先加载配置,但是wifi连接到就绪需要很长时间,所以不能在加载配置以后就去获取IP
 		wifi的就绪我以读取到ip为准,wifi从设置ssid到真正连接ok,都会有事件持续来
 */
 
// 网络状态改变
VOID on_if_state(LPCSTR if_name, UINT8 up, INT32 *save_state, INT32 *delay_ms_to_eth1)
{
	*delay_ms_to_eth1 = 0;
	if (system_quit()) return;

	LOGI("%s() %s=%d, last state=%d", __FUNCTION__, if_name, up, *save_state);

	// 双网口模式下的状态管理
	if (!g_dual_eth_mode) {
		// 单网口模式，只处理eth0
		if (stricmp(if_name, NET_ETH0) != 0)
			return;
	}

	static UINT32 last_state_change_time = 0;
	UINT32 current_time = get_app_uptime();

	// 防止状态抖动 - 至少间隔2秒
	if (current_time - last_state_change_time < 2) {
		LogI("State change too frequent, ignoring");
		return;
	}

	if (!up) {
		// 网络接口断开处理
		if (stricmp(if_name, NET_ETH0) EQU 0) {
			// eth0断开
			LOGW("ETH0 interface down");

			// 确保网口配置独立性
			net_ensure_interface_independence(NET_ETH0, "disconnect");

			if (NET_ST_ETH0 EQU *save_state || NET_ST_DUAL_ETH EQU *save_state) {
				if (g_dual_eth_mode && net_if_ready(NET_ETH1, NULL)) {
					LOGI("Switching from ETH0 to ETH1 (preserving ETH1 independence)");
					*save_state = NET_ST_ETH1; // 切换到eth1
					last_state_change_time = current_time;
				} else {
					LOGW("No available network interface");
					*save_state = NET_ST_NONE;
				}
			}
		}
		else if (stricmp(if_name, NET_ETH1) EQU 0) {
			// eth1断开
			LOGW("ETH1 interface down");

			// 确保网口配置独立性
			net_ensure_interface_independence(NET_ETH1, "disconnect");

			if (NET_ST_ETH1 EQU *save_state || NET_ST_DUAL_ETH EQU *save_state) {
				if (net_if_ready(NET_ETH0, NULL)) {
					LOGI("Switching from ETH1 to ETH0 (preserving ETH0 independence)");
					*save_state = NET_ST_ETH0; // 切换到eth0
					last_state_change_time = current_time;
				} else {
					LOGW("No available network interface");
					*save_state = NET_ST_NONE;
				}
			}
		}
	}
	else {
		// 网络接口连接处理
		if (stricmp(if_name, NET_ETH0) EQU 0) {
			LOGI("ETH0 interface up");

			// 确保网口配置独立性
			net_ensure_interface_independence(NET_ETH0, "connect");

			// 检查是否需要自动保存配置
			CHAR current_ip[32];
			if (net_if_ready(NET_ETH0, current_ip) && strlen(current_ip) > 0) {
				LOGI("ETH0 already has IP: %s, attempting auto save", current_ip);
				net_auto_save_config_on_ready(NET_ETH0);
			}

			// 智能IP分配检测
			if (g_dual_eth_mode && g_smart_ip_enabled && net_dev_carrier(NET_ETH1)) {
				LOGI("Both interfaces connected, performing smart IP allocation detection");

				// 延迟一段时间让网络稳定
				Sleep(2000);

				INT32 smart_state = net_smart_ip_allocation_detect();
				if (smart_state != NET_ST_NONE) {
					*save_state = smart_state;
					LOGI("Smart IP allocation result: state=%d", smart_state);
				} else {
					// 回退到传统逻辑
					if (net_if_ready(NET_ETH1, NULL)) {
						LOGI("Dual Ethernet mode activated (fallback)");
						*save_state = NET_ST_DUAL_ETH;
					} else {
						LOGI("ETH0 single mode activated");
						*save_state = NET_ST_ETH0;
					}
				}
			} else {
				if (g_dual_eth_mode && net_if_ready(NET_ETH1, NULL)) {
					LOGI("Dual Ethernet mode activated (preserving ETH1 independence)");
					*save_state = NET_ST_DUAL_ETH;
				} else {
					LOGI("ETH0 single mode activated");
					*save_state = NET_ST_ETH0;
				}
			}
			last_state_change_time = current_time;
		}
		else if (stricmp(if_name, NET_ETH1) EQU 0) {
			LOGI("ETH1 interface up");

			// 确保网口配置独立性
			net_ensure_interface_independence(NET_ETH1, "connect");

			// 检查是否需要自动保存配置
			CHAR current_ip[32];
			if (net_if_ready(NET_ETH1, current_ip) && strlen(current_ip) > 0) {
				LOGI("ETH1 already has IP: %s, attempting auto save", current_ip);
				net_auto_save_config_on_ready(NET_ETH1);
			}

			// 智能IP分配检测
			if (g_dual_eth_mode && g_smart_ip_enabled && net_dev_carrier(NET_ETH0)) {
				LOGI("Both interfaces connected, performing smart IP allocation detection");

				// 延迟一段时间让网络稳定
				Sleep(2000);

				INT32 smart_state = net_smart_ip_allocation_detect();
				if (smart_state != NET_ST_NONE) {
					*save_state = smart_state;
					LOGI("Smart IP allocation result: state=%d", smart_state);
				} else {
					// 回退到传统逻辑
					if (net_if_ready(NET_ETH0, NULL)) {
						LOGI("Dual Ethernet mode activated (fallback)");
						*save_state = NET_ST_DUAL_ETH;
					} else {
						LOGI("ETH1 single mode activated");
						*save_state = NET_ST_ETH1;
					}
				}
			}
			else {
				if (net_if_ready(NET_ETH0, NULL)) {
					LOGI("Dual Ethernet mode activated (preserving ETH0 independence)");
					*save_state = NET_ST_DUAL_ETH;
				} else {
					LOGI("ETH1 single mode activated");
					*save_state = NET_ST_ETH1;
				}
			}
			last_state_change_time = current_time;
		}
	}

	// 处理网络配置加载
	if (stricmp(if_name, NET_ETH0) EQU 0) {
		if (up) {
			LOGI("Loading ETH0 configuration");

			// 检查是否需要热插拔自动配置
			CHAR current_ip[32];
			UINT8 eth0_ready = net_if_ready(NET_ETH0, current_ip);

			LOGI("ETH0 interface up - ready: %d, IP: %s", eth0_ready,
				 eth0_ready ? current_ip : "none");

			if (g_auto_ip_config_enabled && (!eth0_ready || strlen(current_ip) == 0)) {
				LOGI("Triggering simplified hotplug configuration for ETH0");
				if (net_configure_single_interface(NET_ETH0)) {
					LOGI("ETH0 simplified configuration successful");
					// 配置成功，跳过传统配置
				} else {
					LOGW("ETH0 simplified configuration failed, using traditional logic");
					// 回退到传统逻辑
					goto eth0_traditional_config;
				}
			} else if (eth0_ready) {
				LOGI("ETH0 already configured with IP: %s", current_ip);
			} else {
eth0_traditional_config:
				LOGI("Using traditional configuration for ETH0");
				// 智能IP分配模式下，检查是否需要特殊处理
				if (g_smart_ip_enabled && g_same_segment_detected) {
					LOGI("Same segment detected - loading ETH0 with conflict avoidance");
					net_load_config_smart(NET_ETH0, TRUE);  // 主接口
				} else {
					net_load_config(NET_ETH0);
				}
			}
			sync_time(TIME_CLOCK_SVR, FALSE);
		} else {
			// eth0断开，检查是否需要切换到eth1
			if (g_dual_eth_mode && net_dev_exist(NET_ETH1)) {
				*delay_ms_to_eth1 = 4; // 延时切换避免闪断问题
				LOGI("Scheduled failover to ETH1 in %d seconds", *delay_ms_to_eth1);
			}
		}
	}
	else if (stricmp(if_name, NET_ETH1) EQU 0) {
		if (up) {
			if (g_dual_eth_mode) {
				LOGI("Loading ETH1 configuration");

				// 检查是否需要热插拔自动配置
				CHAR eth1_ip[32];
				UINT8 eth1_ready = net_if_ready(NET_ETH1, eth1_ip);

				LOGI("ETH1 interface up - ready: %d, IP: %s", eth1_ready,
					 eth1_ready ? eth1_ip : "none");

				if (g_auto_ip_config_enabled && (!eth1_ready || strlen(eth1_ip) == 0)) {
					LOGI("Triggering simplified hotplug configuration for ETH1");
					if (net_configure_single_interface(NET_ETH1)) {
						LOGI("ETH1 simplified configuration successful");
						// 配置成功，跳过传统配置
					} 
					else {
						LOGW("ETH1 simplified configuration failed, using traditional logic");
						// 回退到传统逻辑
						goto eth1_traditional_config;
					}
				} 
				else if (eth1_ready) {
					LOGI("ETH1 already configured with IP: %s", eth1_ip);
				} else {
eth1_traditional_config:
					LOGI("Using traditional configuration for ETH1");
					// 智能IP分配模式下，检查是否需要特殊处理
					if (g_smart_ip_enabled) {
						if (g_same_segment_detected) {
							LOGI("Same segment detected - skipping ETH1 configuration to avoid conflicts");
							// 不配置ETH1，避免IP冲突
							return;
						} else {
							LOGI("Different segments - loading ETH1 configuration");
							net_load_config_smart(NET_ETH1, FALSE);  // 辅助接口
						}
					} else {
						net_load_config(NET_ETH1);
					}
				}
				sync_time(TIME_CLOCK_SVR, FALSE);
			}
		}
	}

	// 记录网络状态变化
	LOGI("Network state changed: %s %s, new state=%d",
		 if_name, up ? "UP" : "DOWN", *save_state);
}

INT32 Random(INT32 start, INT32 end){
    INT32 dis = end - start;
	
    return rand() % dis + start;
}


/**
 * 应用有线网卡地址
 * @return   成功=TRUE；失败=FALSE
 */
UINT8 net_apply_net_mac()
{
	CHAR 	tmp[512];
	CHAR 	mac_value[64] = {0};

	QfSet0(mac_value, sizeof(mac_value));
	do {

		// 已经有mac地址了
		if (strlen(g_pEmbedInfo->eth0_mac) > 0) {
			s_strcpy(mac_value, g_pEmbedInfo->eth0_mac);
			break;
		}

		//==========================================
		// 自己生成mac
		//==========================================

		// 根据芯片id产生一个
		{		
			CHAR chip_id[64];
			extern UINT8 vsipc_chipid_mac(CHAR *chip_id,  CHAR *mac_value);
			
			if (vsipc_chipid_mac(chip_id, mac_value)) 
				break;
		}


		// step1. 获取eth1的mac地址用于eth0
		if (!dev_lte_exist() && g_dual_eth_mode && net_if_mac_inc1(NET_ETH1, mac_value, NULL)) {		// 如果有eth1，就使用eth1的mac+1
			if (stricmp(g_pEmbedInfo->eth0_mac, mac_value) != 0) {
				LOGW("modify eth0 mac, [%s] to [%s]", g_pEmbedInfo->eth0_mac, mac_value);
				strcpy(g_pEmbedInfo->eth0_mac, mac_value);
				save_embed_info(g_pEmbedInfo, NULL);
			}
		}
		// step2, 在p2p地址有效的情况下, 使用p2p号第9位后6字节
		else if (stricmp(g_pEmbedInfo->p2pid, INVALID_P2P_ID) != 0) {
			CHAR	*mac_tmp;			
			
			mac_tmp = mac_value;
			for (int i = 0; i < 6; i++) {
				mac_tmp += sprintf(mac_tmp, "%02x:", g_pEmbedInfo->p2pid[8+i]&0xFE);
			}
			*(mac_tmp-1) = 0;
			//LOGI("%s()111 eth0 mac: %s", __func__, mac_value);
		}
		
#if 0	
		// 一般不需要随机产生,批量上电的时候摄像机启动的时候,这些值都是一样的

		// 如果mac地址没有处理处理,就随机产生一个
		if (strlen(mac_value) EQU 0) {
			INT32	i; 
			CHAR	*mac_tmp;
			
			mac_tmp = mac_value;
			srand((unsigned)utc_time());
			for (i = 0; i < 6; i++) {
				mac_tmp += sprintf(mac_tmp, "%02x:", Random(0, 255)&0xFE);
			}
			*(mac_tmp-1) = 0;
			//LOGI("%s()222 eth0 mac: %s", __func__, mac_value);
		}
#endif		
	}while (FALSE);

	LOGI("%s() eth0 mac_value: %s, ethx mac_value: %s, work mac: %s", __func__, g_pEmbedInfo->eth0_mac, g_pEmbedInfo->ethx_mac, mac_value);

	// 修改mac的指令
	char *pos = tmp;
	
	if (net_dev_exist(NET_ETH0) && strlen(mac_value) > 0) {
		//pos += sprintf(pos, "ifconfig " NET_ETH0 " down;");
		pos += sprintf(pos, "ifconfig %s hw ether %s;", NET_ETH0, mac_value);
		//pos += sprintf(pos, "ifconfig " NET_ETH0 " up;");
	}

	// 双网口模式下设置eth1的MAC地址
	if (g_dual_eth_mode && net_dev_exist(NET_ETH1)) {
		if (dev_lte_exist()) {
			pos += sprintf(pos, "ifconfig %s hw ether %s;", NET_ETH1, mac_value);
		}
		else {
			if ((UINT8)g_pEmbedInfo->ethx_mac[0] != 0xff && strlen(g_pEmbedInfo->ethx_mac) > 0) {
				if (pos EQU tmp)
					pos += sprintf(pos, "ifconfig %s hw ether %s;", NET_ETH1, g_pEmbedInfo->ethx_mac);
				else
					pos += sprintf(pos, "ifconfig %s hw ether %s;", NET_ETH1, g_pEmbedInfo->ethx_mac);
			}
		}
	}

	if (pos EQU tmp)
		return FALSE;
	
	return system_run(tmp) EQU 0;
}

/**
 * net初始化
 * @return   成功=OK；失败=FAIL
 */
INT32 net_init()
{
	INT32	scktFd;
	INT32 	iFnRet;
	INT32 	sckt_value;
	struct sockaddr_in tSockAddr;

	//==================================================
	// 网络搜索	
	//==================================================

	scktFd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
 	if (scktFd < 0) {
		LOGE("socket() error=%d", errno);
		return FAIL;
	}

	INT32 iReuseAddress = 1;   
	iFnRet = setsockopt(scktFd, SOL_SOCKET, SO_REUSEADDR,   
						(char *)&iReuseAddress, sizeof(iReuseAddress)); 
	if (SOCKET_ERROR EQU iFnRet) {
		LOGE("setsockopt() error=%d", errno);
		goto FN_END;
	}

	tSockAddr.sin_port   = htons(VS_PORT_SRCH_SVR);
	tSockAddr.sin_family = AF_INET;
	tSockAddr.sin_addr.s_addr = INADDR_ANY; 
	iFnRet = bind(scktFd, (struct sockaddr*)&tSockAddr, sizeof(tSockAddr));	
	if (iFnRet EQU SOCKET_ERROR) {
		LOGE("bind() error=%d", errno);
		goto FN_END;
	}

	// 广播
	sckt_value = TRUE ;
	iFnRet = setsockopt(scktFd, SOL_SOCKET, SO_BROADCAST, (char *)&sckt_value, sizeof(sckt_value));
	if (SOCKET_ERROR EQU iFnRet) {
		LOGE("setsockopt(SO_BROADCAST) error=%d", errno);
	}

	// loopback
	sckt_value = FALSE;
	iFnRet = setsockopt(scktFd, SOL_SOCKET, IP_MULTICAST_LOOP, (char *)&sckt_value, sizeof(sckt_value));
	if (SOCKET_ERROR EQU iFnRet) {
		LOGE("setsockopt(IP_MULTICAST_LOOP) error=%d", errno);
	}

	// thread
	PTHREAD_CREATE(&g_workThread, NULL, net_srch_cmd_thread, (LPVOID)scktFd);

	// 启动时自动IP配置（使用简化逻辑）
	if (g_auto_ip_config_enabled) {
		LOGI("Starting simplified startup auto IP configuration...");

		// 在单独的线程中执行简化的自动配置
		pthread_t startup_config_thread;
		if (PTHREAD_CREATE(&startup_config_thread, NULL, net_simplified_startup_thread, NULL) == 0) {
			LOGI("Simplified startup auto IP configuration thread created");
			pthread_detach(startup_config_thread);  // 分离线程，自动清理
		} else {
			LOGE("Failed to create simplified startup auto IP configuration thread");
			// 如果线程创建失败，直接执行简化配置
			net_simplified_auto_config();
		}
	}

	LOGI("net module init.");

	return OK;

FN_END:
	close(scktFd);

	return FAIL;
	
}

/**
 * net反初始化
 */
VOID net_uninit()
{
	WAIT_THREAD(g_workThread, NULL);

	net_ifdetect_stop();

	LOGI("net module uninit.");
}

/**
 * 判断网络设备是否存在
 * @return   返回1表示存在；否返回0	
 */
UINT8 net_dev_exist(LPCSTR if_name)
{
	CHAR	dev_path[64] = {0};

	_snprintf(dev_path, sizeof(dev_path)-1, "/sys/class/net/%s", if_name);

	return dir_exist(dev_path);
}

/**
 * 判断网络设备物理连接是否可用, 但是可能由于dhcp或者ip冲突不可用
 *	有线网络网线已经插入
 *	wifi必须已经配上ssid并连接上
 * @return   返回1表示可用；否返回0	
 */
UINT8 net_dev_carrier(LPCSTR if_name)
{
	INT32	fd;
	CHAR	dev_path[64] = {0};
	
	_snprintf(dev_path, sizeof(dev_path)-1, "/sys/class/net/%s/carrier", if_name);

	fd= open(dev_path, O_RDONLY);
	if (fd > 0) {
		UINT8	result = FALSE;
		
		if (read(fd, dev_path, sizeof(dev_path)) > 0) {
			if (atoi(dev_path) > 0) {
				result = TRUE;
			}
		}
		close(fd);

		return result;
	}

	return FALSE;
}



/**
 * 判断网络是否运行并处理就绪状态
 * 	就绪时,如果ip!=NULL,将ip地址存储在里面 
 * @return   返回1表示就绪；否返回0	
 */
UINT8 net_if_ready(LPCSTR if_name, LPSTR ip)
{
	int skfd = 0;
    struct ifreq ifr;

	// 初始化串
	if (ip) ip[0] = 0;

	// 判断设备是否存在
	if (if_name EQU NULL || !net_dev_exist(if_name))
		return FALSE;
	
    skfd = socket(AF_INET, SOCK_DGRAM, 0);
    if(skfd < 0)
    {
        LOGE("Open socket error, errno = %d!\n", errno);
        return FALSE;
    }
	
    strcpy(ifr.ifr_name, if_name);
#if 1	
	do {
		// 检测网卡状态
		if (ioctl(skfd, SIOCGIFFLAGS, &ifr) < 0) {
	        LOGE("Maybe ethernet inferface %s is not valid! errno=%d\n",  if_name, errno);  
	        break;
	    }
		if (ifr.ifr_flags & IFF_RUNNING) {
			int i;
			
			for (i = 0; i < MAX_TRY_COUNT; i++) {
				if (ioctl(skfd, SIOCGIFADDR, &ifr) < 0) {  
//			        LOGE("%s SIOCGIFADDR IOCTL error! errno=%d\n",  if_name, errno);  
					Sleep(1000);
					continue;
			    } 
				break;
			}
			if (i EQU MAX_TRY_COUNT)
				break;
			
		    struct sockaddr_in *pAddr = (struct sockaddr_in *)&(ifr.ifr_addr); 
			//LOGI("%s ip addr :[%s]", if_name, inet_ntoa(pAddr->sin_addr)); 
			if (pAddr->sin_addr.s_addr EQU 0) {	// 检测是否得到IP
				break;
			}

			if (ip != NULL) strcpy(ip, inet_ntoa(pAddr->sin_addr));

			close(skfd);

			return TRUE;
		}
	}while (0);
	close(skfd);

	return  FALSE;	
#else
	// 检测网络是否通(但是wifi下会有问题)
	struct ethtool_value edata;
	edata.cmd = ETHTOOL_GLINK;
	edata.data = 0;   
	ifr.ifr_data = (char *) &edata;
	if (ioctl(skfd, SIOCETHTOOL, &ifr) < 0) {
		
		LOGE("Maybe ethernet inferface %s is not valid!errno=%d", if_name, errno); // wifi则不行
        close(skfd);
        return FALSE;    
    }  

	close(skfd);
	fprintf(stdout, "%s Link detected: %s\n",  
                    if_name, edata.data ? "yes":"no");  
	
    return (edata.data) ? TRUE : FALSE;	
#endif	
}


/**
 * 得到指定网络设备的MTU
 * @return   返回>0表示成功; 否则-1	
 */
INT32 net_get_mtu(LPCSTR if_name)
{
	int skfd = 0;
	struct ifreq ifr;

	// 判断设备是否存在
	if (if_name EQU NULL || !net_dev_exist(if_name))
		return -1;
	
	skfd = socket(AF_INET, SOCK_DGRAM, 0);
	if(skfd < 0)
	{
		LOGE("Open socket error!");
		return -1;
	}
	
	strcpy(ifr.ifr_name, if_name);
	if (ioctl(skfd, SIOCGIFMTU, &ifr) < 0) {  
		close(skfd);
		LOGE("if=%s, get mtu error", if_name);	
		return -1;
	}
	close(skfd);

	return ifr.ifr_mtu;

}

/**
 * 设置指定网络设备的MTU
 * @return   返回=0表示成功; 否则-1	
 */
INT32 net_set_mtu(LPCSTR if_name, INT32 mtu_size)
{
	int skfd = 0;
	struct ifreq ifr;

	// 判断设备是否存在
	if (if_name EQU NULL || !net_dev_exist(if_name))
		return -1;
	
	skfd = socket(AF_INET, SOCK_DGRAM, 0);
	if(skfd < 0)
	{
		LOGE("Open socket error!");
		return -1;
	}
	
	strcpy(ifr.ifr_name, if_name);
	ifr.ifr_mtu = mtu_size;
	if (ioctl(skfd, SIOCSIFMTU, &ifr) < 0) {  
		close(skfd);
		LOGE("if=%s, mtu=%d, set mtu error", if_name, mtu_size);	
		return -1;
	}
	close(skfd);

	return 0;

}


/**
 * 得到网关地址
 * if_name为空时,将获取默认网关地址,成功,将ip地址存储在里面
 * @return   返回1表示就绪；否返回0	
 */
UINT8 net_gw_addr(LPCSTR if_name, LPCHAR ip)
{
#if 0
	CHAR	cmdline[120];
	CHAR	readline[300];
	FILE	*fp;

	if (if_name) {
		sprintf(cmdline, "ip route|grep %s|grep default|cut -d ' ' -f 3", if_name);
	}
	else {
		strcpy(cmdline, "ip route|grep default|cut -d ' ' -f 3");
	}
	*ip = 0;

	fp = popen(cmdline, "r");
	if (fp EQU NULL)
		return FALSE;
		
	while ( NULL != fgets(readline, sizeof(readline), fp)) {
		strncpy(ip, readline, strlen(readline)-1);		// "\n"
		break;
	}	
	pclose(fp);

	return *ip != 0 ? TRUE : FALSE;
#else
	FILE *pfile = fopen("/proc/net/route", "r");    

	*ip = 0;
	
    if (pfile != NULL){  
		char	*line = NULL;
		size_t	nums = 0;	
	 	char 	iface[32];
		UINT32 	dest_addr=0, gate_addr=0;
		
		while (getline(&line, &nums, pfile) != -1){
			
			if (sscanf(line, "%s\t%x\t%x", iface, &dest_addr, &gate_addr) EQU 3 
				&& dest_addr EQU 0
				&& gate_addr != 0) {
//				LogD("GateWay iface=%s", iface);
				if (!if_name || strncmp(if_name, iface, strlen(if_name)) EQU 0){
		            sprintf(ip, "%hhu.%hhu.%hhu.%hhu", 
		            		gate_addr&0xff, (gate_addr>>8)&0xff, 
		            		(gate_addr>>16)&0xff, (gate_addr>>24)&0xff);
//					LogD("GateWay ip=%s", ip);
					free(line);
					fclose(pfile);
					return TRUE;
				}
			}
			
			free(line);
			line = NULL;
			nums = 0;			
		}	
		fclose(pfile);	   
    }

	return FALSE;
#endif	

	
}

/** 
 * 判断tcp是否严重拥堵
 * @return  返回拥堵量: -1(表示没有连接), 0-10
 */
INT32 net_tcp_jam()
{
	UINT32	total=0, retry = 0;
	UINT32 	no;
	UINT32 	local_port;
	UINT32 	rem_port;
	UINT32 	status;
	UINT32 	tx_queue;
	UINT32 	rx_queue;
	UINT32	tr,tm_when,retrnsmt;
	FILE	*pfile;
	LPCSTR	tcps[] = {"/proc/net/tcp", "/proc/net/tcp6"};

	for (int i = 0; i < QfLength(tcps); i++){
		pfile = fopen(tcps[i], "r");
	    if (pfile != NULL){  
			char	*line = NULL;
			size_t	nums = 0;	
			
			while (getline(&line, &nums, pfile) != -1){
				
				if (sscanf(line, "%u: %*[^:]:%x %*[^:]:%x %x %x:%x %x:%x %x\n",
		                  &no, &local_port, &rem_port,
		                  &status, &tx_queue, &rx_queue,
		                  &tr, &tm_when, &retrnsmt) EQU 9
					&& status EQU TCP_ESTABLISHED) {

					total++;
					if (retrnsmt > 0)
						retry++;	
				}
				
				free(line);
				line = NULL;
				nums = 0;
			}	
			fclose(pfile);	   
	    }
	}

	if (total EQU 0)
		return -1;

	return retry*10/total;
}


/**
 * 得到网卡mac地址
 * @return   返回1表示就绪；否返回0	
 */
UINT8 net_if_mac(LPCSTR if_name, LPCHAR mac, LPCSTR fmt)
{
	int skfd = 0;
	struct ifreq ifr;

	// 初始化串
	if (mac) mac[0] = 0;
	
	skfd = socket(AF_INET, SOCK_DGRAM, 0);
	if(skfd < 0)
	{
		LOGE("Open socket error!\n");
		return FALSE;
	}
	
	strcpy(ifr.ifr_name, if_name);
	do {

		// 检测网卡状态
		if (ioctl(skfd, SIOCGIFFLAGS, &ifr) < 0) {
			LOGE("Maybe ethernet inferface %s is not valid! errno=%d\n",  if_name, errno);	
			break;
		}		
		 	
//		if (ifr.ifr_flags & IFF_RUNNING) {
			int i;
			
			for (i = 0; i < MAX_TRY_COUNT; i++) {
				if (ioctl(skfd, SIOCGIFHWADDR,  &ifr) < 0) {  
//					LOGE("%s SIOCGIFADDR IOCTL error! errno=%d\n",	if_name, errno);  
					Sleep(1000);
					continue;
				} 
				break;
			}
			if (i EQU MAX_TRY_COUNT)
				break;
			
			if (mac != NULL) {

                sprintf(mac, fmt EQU NULL ? "%02x:%02x:%02x:%02x:%02x:%02x" : fmt,
                    (UINT8)ifr.ifr_hwaddr.sa_data[0],
                    (UINT8)ifr.ifr_hwaddr.sa_data[1],
                    (UINT8)ifr.ifr_hwaddr.sa_data[2],
                    (UINT8)ifr.ifr_hwaddr.sa_data[3],
                    (UINT8)ifr.ifr_hwaddr.sa_data[4],
                    (UINT8)ifr.ifr_hwaddr.sa_data[5]);
			}

			close(skfd);

			return TRUE;
		//}
	}while (0);
	close(skfd);

	return	FALSE;	

}


UINT8 net_if_mac_inc1(LPCSTR if_name, LPCHAR mac, LPCSTR fmt)
{
	int skfd = 0;
	struct ifreq ifr;

	// 初始化串
	if (mac) mac[0] = 0;
	
	skfd = socket(AF_INET, SOCK_DGRAM, 0);
	if (skfd < 0)
	{
		LOGE("Open socket error!\n");
		return FALSE;
	}
	
	strcpy(ifr.ifr_name, if_name);
	do {

		// 检测网卡状态
		if (ioctl(skfd, SIOCGIFFLAGS, &ifr) < 0) {
			LOGE("Maybe ethernet inferface %s is not valid! errno=%d\n",  if_name, errno);	
			break;
		}		
		 	
//		if (ifr.ifr_flags & IFF_RUNNING) {
			int i;
			
			for (i = 0; i < MAX_TRY_COUNT; i++) {
				if (ioctl(skfd, SIOCGIFHWADDR,  &ifr) < 0) {  
//					LOGE("%s SIOCGIFADDR IOCTL error! errno=%d\n",	if_name, errno);  
					Sleep(1000);
					continue;
				} 
				break;
			}
			if (i EQU MAX_TRY_COUNT)
				break;
			
			if (mac != NULL) {

                sprintf(mac, fmt EQU NULL ? "%02x:%02x:%02x:%02x:%02x:%02x" : fmt,
                    (UINT8)ifr.ifr_hwaddr.sa_data[0],
                    (UINT8)ifr.ifr_hwaddr.sa_data[1],
                    (UINT8)ifr.ifr_hwaddr.sa_data[2],
                    (UINT8)(ifr.ifr_hwaddr.sa_data[3]+1),
                    (UINT8)ifr.ifr_hwaddr.sa_data[4],
                    (UINT8)ifr.ifr_hwaddr.sa_data[5]);
			}

			close(skfd);

			return TRUE;
		//}
	}while (0);
	close(skfd);

	return	FALSE;	

}

/**
 * 得到有效的mac地址,检测步骤是从NET_ETH1=>g_pEmbedInfo->ethx_mac=>NET_ETH0=>g_pEmbedInfo->eth0_mac
 *
 * @return   返回1表示就绪；否返回0
 */
UINT8 net_valid_if_mac(LPCSTR if_name, LPCHAR mac, LPCSTR fmt)
{

	// eth1
	do {
		if (stricmp(if_name, NET_ETH1) EQU 0) {

			if (strlen(g_pEmbedInfo->ethx_mac) EQU 0) {
				if (net_dev_exist(NET_ETH1))
					return net_if_mac(if_name, mac, fmt);
				break;
			}

			return net_parse_mac(g_pEmbedInfo->ethx_mac, mac, fmt);
		}

	} while (FALSE);

	do {
		//if (stricmp(if_name, NET_ETH0) EQU 0) {			

			//LOGI("%s() NET_ETH0:2222 %s", __func__, g_pEmbedInfo->eth0_mac);
			if (strlen(g_pEmbedInfo->eth0_mac) EQU 0) {
				//LOGI("%s() NET_ETH0:11111 %s", __func__, g_pEmbedInfo->eth0_mac);
				if (net_dev_exist(NET_ETH0))
					return net_if_mac(NET_ETH0, mac, fmt);
				break;
			}

			//LOGI("%s() NET_ETH0: %s", __func__, g_pEmbedInfo->eth0_mac);
			return net_parse_mac(g_pEmbedInfo->eth0_mac, mac, fmt);
		//}
	
	} while (FALSE);

	return FALSE;
}


/** 
 * 解析MAC地址
 * @param  src_mac  源mac地址
 * @param  mac  返回的MAC地址
 * @param  fmt=NULL  格式
 * @return  返回1表示就绪；否返回0	
 */
UINT8 net_parse_mac(LPCSTR src_mac, LPCHAR mac, LPCSTR fmt)
{
	UINT32	mac6[6];

	// 初始化串
	if (mac) mac[0] = 0;

	QfSet0(mac6, sizeof(mac6));
    sscanf(src_mac, "%02x:%02x:%02x:%02x:%02x:%02x",
           &mac6[0],
           &mac6[1],
           &mac6[2],
           &mac6[3],
           &mac6[4],
           &mac6[5]);			
	if (mac != NULL) {
        sprintf(mac, fmt EQU NULL ? "%02x:%02x:%02x:%02x:%02x:%02x" : fmt,
            (UINT8)mac6[0],
            (UINT8)mac6[1],
            (UINT8)mac6[2],
            (UINT8)mac6[3],
            (UINT8)mac6[4],
            (UINT8)mac6[5]);
		
		return TRUE;
	}

	return FALSE;
}


/**
 * 网络异常判断(只判断非有线网络)
 */
UINT8 network_abnormal()
{
	// 有线网络不做判断
	if (stricmp(net_get_work_ifname(), NET_ETH0) != 0) {
		UINT32 	t_pkts[2], r_pkts[2], t_drop[2], r_drop[2], carr;

		for (INT32 i = 0; i < 2; i++) {
			if (!net_dev_status(net_get_work_ifname(), &t_pkts[i], &r_pkts[i], &t_drop[i], &r_drop[i], &carr))	{
				LOGE_NF("net_get_status[%d] fail", i);
				return TRUE;
			}
			if (i EQU 0){
				Sleep(2000);
			}
		}

		if (t_pkts[0] EQU t_pkts[1]
			&& r_pkts[0] EQU r_pkts[1]) {
			LOGW_NF("net_get_status, r_pkts=%u, t_pkts=%u", r_pkts[0], t_pkts[0]);
			return TRUE;
		}
		
	}
	
	return FALSE;
}


#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wsign-compare"
LPVOID if_detect_thread(LPVOID)
{
	THREAD_FUNC_BEGIN();

	UINT8	loop = 1;
	INT32 	fnRet;
	ULONG	lose_net_time = 0;
	UINT8	net_except = FALSE;
    CHAR 	*buf = (CHAR *)malloc(BUFLEN);
	CHAR	ip[32];
    INT32 	len;	
	fd_set		read_fds;
	timeval 	tv;	
	struct nlmsghdr 	*nh;
    struct ifinfomsg 	*ifinfo;
    struct rtattr 		*attr;
	INT32	delay_ms_to_eth1 = 0;

	// vs_logdb_set_env(VS_SOURCE_SYS, IPC_MOD_NET);
	while (loop &&
	 		g_fd_detect &&
	 		!system_quit())
	{
#ifdef NET_ABNORMAL_REBOOT_IPC	
		// 脱网将重启
		if (net_except
			&& network_abnormal()			
			&& !g_pRunSets->misc_set.disable_reboot
			&& get_app_uptime() - lose_net_time > NET_ABNORMAL_TIMEOUT_INTERFACE) {
			LOGI("if_detect_thread(). reboot");
			set_except_flag();
			system_reboot();
			break;
		}
#endif

		wdt_live();

   		FD_ZERO(&read_fds);  
		FD_SET(g_fd_detect, &read_fds);
		tv.tv_sec 	= 1; // 超时
		tv.tv_usec 	= 0; 		
		fnRet = ::select(g_fd_detect+1, &read_fds, NULL, NULL, &tv);//检测socket是否有效
		if (fnRet < 0) {

			if (errno EQU EINTR)
				continue;
			
			LogW("select(). errno=%d", errno);
			break;		
		}
		
		if (fnRet EQU 0 || 
			!FD_ISSET(g_fd_detect, &read_fds)) {

			//LOGI("if_detect_thread(). state: %d", g_if_save_state);

			// 检测网络状态
			static UINT32 last_quality_check = 0;
			UINT32 current_time = get_app_uptime();

			if (g_if_save_state EQU NET_ST_NONE) {

				// 处理延时问题
				if (delay_ms_to_eth1) {
					LOGI_NF(" delay=%d, state=%d", delay_ms_to_eth1, g_if_save_state);
					if (--delay_ms_to_eth1 EQU 0) {
						net_load_config(NET_ETH1);
					}
					continue;
				}

				// 使用智能网络模式适应
				INT32 new_state = net_dual_eth_load_balance();

				if (new_state != NET_ST_NONE) {
					sync_time(TIME_CLOCK_SVR, FALSE);
					g_if_save_state = new_state;
					net_except = FALSE;

					// 根据状态记录日志
					switch (new_state) {
						case NET_ST_DUAL_ETH:
							if (g_smart_ip_enabled && g_network_segment_detected) {
								LOGI("Dual Ethernet mode active - different network segments");
							} else {
								LOGI("Dual Ethernet mode active");
							}
							break;
						case NET_ST_ETH0:
							if (g_smart_ip_enabled && g_same_segment_detected) {
								LOGI("ETH0 connection active - same segment detected, ETH1 disabled");
							} else {
								LOGI("ETH0 connection active");
							}
							break;
						case NET_ST_ETH1:
							LOGI("ETH1 connection active");
							break;
					}
				}
				else {
					LOGE("[%s] net_except - no ethernet interface available",  net_get_work_ifname());
					net_except = TRUE;
					if (lose_net_time EQU 0){
						lose_net_time = get_app_uptime();
						// LOGDB_ERR(ETHERNET_DISCONNECTED);
					}
				}

			}
			else {
				// 简化的网络状态检查 (每30秒)
				if (current_time - last_quality_check > 30) {
					last_quality_check = current_time;

					// 基本的网络状态检查
					if (g_dual_eth_mode) {
						UINT8 eth0_ready = net_if_ready(NET_ETH0, NULL);
						UINT8 eth1_ready = net_if_ready(NET_ETH1, NULL);

						LOGI("Network status check: ETH0=%d, ETH1=%d", eth0_ready, eth1_ready);

						// 简单的故障转移逻辑
						LPCSTR current_if = net_get_work_ifname();
						if (stricmp(current_if, NET_ETH0) == 0 && !eth0_ready && eth1_ready) {
							LOGI("ETH0 failed, switching to ETH1");
							net_load_config(NET_ETH1);
							g_if_save_state = NET_ST_ETH1;
						} else if (stricmp(current_if, NET_ETH1) == 0 && !eth1_ready && eth0_ready) {
							LOGI("ETH1 failed, switching to ETH0");
							net_load_config(NET_ETH0);
							g_if_save_state = NET_ST_ETH0;
						}
					}
				}

				// 检查当前连接状态
				if (g_if_save_state EQU NET_ST_ETH0) {
					if (!net_if_ready(NET_ETH0, ip)) {
						LOGE(" [%s] ip lose", NET_ETH0);
						net_except = TRUE;
						if (lose_net_time EQU 0){
							lose_net_time = get_app_uptime();
							// LOGDB_ERR(ETH0_DISCONNECTED);
						}

						// 尝试故障转移
						if (g_dual_eth_mode && net_if_ready(NET_ETH1, NULL)) {
							LOGI("Auto failover from ETH0 to ETH1");
							g_if_save_state = NET_ST_ETH1;
							net_load_config(NET_ETH1);
						} else {
							g_if_save_state = NET_ST_NONE;
						}
						continue;
					}
					else if (net_except){
						LOGI("ETH0 connection recovered");
						// LOGDB_INFO(ETH0_CONNECTION_SUCC);
					}
				}
				else if (g_if_save_state EQU NET_ST_ETH1) {
					if (!net_if_ready(NET_ETH1, ip)) {
						LOGE(" [%s] ip lose", NET_ETH1);
						net_except = TRUE;
						if (lose_net_time EQU 0){
							lose_net_time = get_app_uptime();
							// LOGDB_ERR(ETH1_DISCONNECTED);
						}

						// 尝试故障转移
						if (net_if_ready(NET_ETH0, NULL)) {
							LOGI("Auto failover from ETH1 to ETH0");
							g_if_save_state = NET_ST_ETH0;
							net_load_config(NET_ETH0);
						} else {
							g_if_save_state = NET_ST_NONE;
						}
						continue;
					}
					else if (net_except){
						LOGI("ETH1 connection recovered");
						// LOGDB_INFO(ETH1_CONNECTION_SUCC);
					}
				}
				else if (g_if_save_state EQU NET_ST_DUAL_ETH) {
					UINT8 eth0_ready = net_if_ready(NET_ETH0, NULL);
					UINT8 eth1_ready = net_if_ready(NET_ETH1, ip);

					if (!eth0_ready && !eth1_ready) {
						LOGE("Both ethernet interfaces lost");
						net_except = TRUE;
						g_if_save_state = NET_ST_NONE;
						if (lose_net_time EQU 0){
							lose_net_time = get_app_uptime();
							// LOGDB_ERR(DUAL_ETH_DISCONNECTED);
						}
						continue;
					}
					else if (!eth0_ready && eth1_ready) {
						LOGW("ETH0 lost in dual mode, switching to ETH1 only");
						g_if_save_state = NET_ST_ETH1;
						net_load_config(NET_ETH1);
					}
					else if (eth0_ready && !eth1_ready) {
						LOGW("ETH1 lost in dual mode, switching to ETH0 only");
						g_if_save_state = NET_ST_ETH0;
						net_load_config(NET_ETH0);
					}
					else if (net_except){
						LOGI("Dual Ethernet connection recovered");
						// LOGDB_INFO(DUAL_ETH_CONNECTION_SUCC);
					}
				}
				net_except = FALSE;
				lose_net_time = 0;
			}
			
			continue;
		}
		
    	fnRet = read(g_fd_detect, buf, BUFLEN);
        for (nh = (struct nlmsghdr *)buf; NLMSG_OK(nh, fnRet); nh = NLMSG_NEXT(nh, fnRet))
        {
            if (nh->nlmsg_type == NLMSG_DONE)
                break;
            else if (nh->nlmsg_type == NLMSG_ERROR) {
				loop = 0;
				break;
			} else if (nh->nlmsg_type != RTM_NEWLINK)
                continue;

			ifinfo = (ifinfomsg*)NLMSG_DATA(nh);          
            attr = (struct rtattr*)(((char*)nh) + NLMSG_SPACE(sizeof(*ifinfo)));
            len = nh->nlmsg_len - NLMSG_SPACE(sizeof(*ifinfo));

			for (; RTA_OK(attr, len); attr = RTA_NEXT(attr, len))
            {
                if (attr->rta_type == IFLA_IFNAME)
                {
//                	LOGI_NF("%s=%#x, change_mask=%#x, last state=%d",  
//						(char*)RTA_DATA(attr), (UINT32)ifinfo->ifi_flags, (UINT32)ifinfo->ifi_change, g_if_save_state);
					on_if_state((char*)RTA_DATA(attr), (ifinfo->ifi_flags & IFF_LOWER_UP) ? 1 : 0, &g_if_save_state, &delay_ms_to_eth1);
                    goto fn_read_next;
                }
            }
        }

fn_read_next:
		;

    }
	free(buf);
			
	THREAD_FUNC_END();
}
#pragma GCC diagnostic pop

/**
 * 开启网络接口检测
 * @return   成功=OK；失败=FAIL
 */
INT32 net_ifdetect_start()
{
	INT32	len = BUFLEN;
	INT32	scktFd = socket(AF_NETLINK, SOCK_RAW, NETLINK_ROUTE);
	struct sockaddr_nl addr;

	if (scktFd < 0) {
		LOGE("socket() error=%d", errno);
		return FAIL;
	}
	
    setsockopt(scktFd, SOL_SOCKET, SO_RCVBUF, &len, sizeof(len));
    memset(&addr, 0, sizeof(addr));
    addr.nl_family = AF_NETLINK;
    addr.nl_groups = RTNLGRP_LINK;
    bind(scktFd, (struct sockaddr*)&addr, sizeof(addr));
	g_fd_detect = scktFd;
	
	// thread
	PTHREAD_CREATE(&g_ifd_thread, NULL, if_detect_thread, NULL);

	LOGI(__FUNCTION__);
	
	return OK;
}

/**
 * 停止网络接口检测
 */
VOID net_ifdetect_stop()
{
	LOGI(__FUNCTION__);

	if (g_fd_detect != 0) {
		close(g_fd_detect);
		g_fd_detect = 0;
	}	

	WAIT_THREAD(g_ifd_thread, NULL);
}

/**
 * 网络截入配置
 * @param  if_name 网络名,如果为NULL则自动检测
 * @return         成功=OK；失败=FAIL
 */
static CHAR  g_cur_if_name[32];
INT32 net_load_config(LPCSTR if_name)
{

#if defined(LH_LWJ)
	return 0;
#endif

	
	UINT8	is_eth1 = FALSE;
	UINT8	dual_eth_mode = g_dual_eth_mode;
	FILE	*js_file = fopen(JS_NET_FILE, "w");
	T_SET_NETWORK	*net = NULL;
	UINT8	wpa_supp = FALSE;

	if (js_file EQU NULL)
	{
		LOGE("%s, fopen(), failed, errcode=%d", __FUNCTION__, errno);
		return FAIL;
	}
	set_wpa_ready(FALSE);
	fprintf(js_file, "#! /bin/sh\n");
	// 释放内存
	//fprintf(js_file, "echo 3 > /proc/sys/vm/drop_caches;\n");

	LOGI("%s, if_name=%s", __FUNCTION__, if_name);

	// 判断是否为明确指定的接口
	UINT8 explicit_interface = (if_name != NULL && strlen(if_name) > 0);

	if (explicit_interface) {
		// 明确指定接口：严格按照指定的网卡进行配置
		LOGI("Using explicitly specified interface: %s", if_name);

		// 仅进行基本的接口存在性检查
		if (!net_dev_exist(if_name)) {
			LOGE("Specified interface %s does not exist", if_name);
			return FAIL;
		}

		LOGI("Interface %s exists, proceeding with configuration", if_name);
	} else {
		// 自动检测模式：使用简化的物理连接检测
		LOGI("Auto-detecting interface based on physical connection");

		UINT8 eth0_carrier = net_dev_carrier(NET_ETH0);
		UINT8 eth1_carrier = dual_eth_mode ? net_dev_carrier(NET_ETH1) : FALSE;

		LOGI("Physical connection status: ETH0=%d, ETH1=%d", eth0_carrier, eth1_carrier);

		if (dev_lte_exist()) {	// 有4G模块的情况
			if (eth0_carrier || net_if_ready(NET_ETH0, NULL) || !g_pSystemSets->stat.lte_mode) {
				if_name = NET_ETH0;
			}
			else if (dual_eth_mode && net_dev_exist(NET_ETH1) && eth1_carrier) {
				if_name = NET_ETH1;
			}
		}
		else {
			// 基于物理连接的简单选择逻辑
			if (eth0_carrier) {
				if_name = NET_ETH0;
			}
			else if (dual_eth_mode && eth1_carrier) {
				if_name = NET_ETH1;
			}
			else {
				// 如果都没有物理连接，检查已配置的接口
				if (net_if_ready(NET_ETH0, NULL)) {
					if_name = NET_ETH0;
				}
				else if (dual_eth_mode && net_if_ready(NET_ETH1, NULL)) {
					if_name = NET_ETH1;
				}
			}
		}

		LOGI("Auto-selected interface: %s", if_name ? if_name : "none");
	}

	s_strcpy(g_cur_if_name, if_name);
	
	if (dev_lte_exist()) {	// 有4G模块的情况		

		LOGI("lte_mode=%d, apn_name=%s", 
			g_pSystemSets->stat.lte_mode, DEV_LTE_APN_NAME);
		
		// LTE functionality removed for dual Ethernet mode
	}
	

	if (!is_mount_mode()) {

		// 仅清除当前接口的IP地址，保护其他已配置接口
		fprintf(js_file, "ip addr flush dev %s;\n", if_name);
			
		// eth0网络
		if (stricmp(if_name, NET_ETH0) EQU 0) {
			net = &g_pRunSets->eth0;

			// 清除eth1网关和路由
			if (dual_eth_mode) {
				fprintf(js_file, "while route del default gw 0.0.0.0 dev %s ; do echo; done;\n", NET_ETH1);
				fprintf(js_file, "ip route | grep \"%s\" | while read line; do ip route del $line; done;\n", NET_ETH1);
			}
		}
		// eth1网络
		else if (stricmp(if_name, NET_ETH1) EQU 0) {
			net = &g_pRunSets->eth1;
			is_eth1 = TRUE;

			// 清除eth0网关和路由
			fprintf(js_file, "while route del default gw 0.0.0.0 dev %s ; do echo; done;\n", NET_ETH0);
			fprintf(js_file, "ip route | grep \"%s\" | while read line; do ip route del $line; done;\n", NET_ETH0);
		}
		else {
			// 默认使用eth0配置
			net = &g_pRunSets->eth0;
		}
	//	g_cur_if = if_name;

		// 杀掉脚本
		fprintf(js_file, "ps -ef|grep -i \[n]et_scpt.sh|grep -v $$|tr -s ' '|cut -d' ' -f2|xargs kill -9\n");
		//system_run("killall -9 net_scpt.sh\n");
	
		// 杀掉ecm.sh
		fprintf(js_file, "killall -9 ecm.sh;\n");

		// 校时
		fprintf(js_file, "killall -9 ntpd 1>/dev/null 2>&1\n");				// ntp
		fprintf(js_file, "killall -9 udhcpc 1>/dev/null 2>&1\n");			//	>/dev/null 2>&1
		fprintf(js_file, "rm %s\n", CFG_NETWORK("/tmp/", 0));

		LOGI("%s, if_name=%s, is_eth1=%d", __FUNCTION__, if_name, is_eth1);

		// 网络配置设置
		do {
			// 双网口模式下的处理
			// 所有配置都通过标准以太网接口处理
		
			// IP地址, 子网掩码以及网关
			if (!net->dhcp 
				&& strlen(net->ip) > 0
				&& strlen(net->netmask) > 0
				&& strlen(net->gateway) > 0) {

				LOGI("specified %s ip[%s], netmask[%s], gateway[%s]", if_name, net->ip, net->netmask, net->gateway);
				fprintf(js_file, "ifconfig %s %s netmask %s\n", if_name, net->ip, net->netmask);
				fprintf(js_file, "route add default gw %s\n", net->gateway);
				fprintf(js_file, "cp -f %s /tmp/\n", CFG_NETWORK(CFG_PATH, 0));

				// dns				
				{
					FILE *pfile = fopen("/tmp/resolv.conf", "wb");
					
					if (pfile) {
						if (net->dns[0][0]) {	// 有配置dns
							fprintf(pfile, "nameserver %s\n", net->dns[0]);
							if (net->dns[1][0]) 
								fprintf(pfile, "nameserver %s\n", net->dns[1]);
						}
						else {		// 未配置dns
							fprintf(pfile, "nameserver ***************\n");
							fprintf(pfile, "nameserver *********\n");
							fprintf(pfile, "nameserver ***********\n");
							fprintf(pfile, "nameserver *******\n");
						}
						fflush(pfile);
						fclose(pfile);
					}
				}		
				
			} 
			else {

				char	dhcpc_cfg[64];

				LOGI("dhcp %s ", if_name);
				strcpy(dhcpc_cfg, "/opt/dhcpc.conf");
				
				// dhcp
				fprintf(js_file, "killall -9 udhcpc 1>/dev/null 2>&1\n");	// 不做这次可能有重复
#if defined(GB28181_ENABLE) && (GB_PLATFORM_ID==GB_PFID_SCDX_MJ) 
				// 为兼容升级版本
				{
					fprintf(js_file, "cp -f /opt/dhcpc.conf /tmp/dhcpc.conf\n");
					fprintf(js_file, "cp -f /opt/app/dhcpc.conf /tmp/dhcpc.conf\n");
					fprintf(js_file, "sed -i 's#www.baidu.com#gateway.mj.sctel.com.cn#g' /tmp/dhcpc.conf\n"); 
					strcpy(dhcpc_cfg, "/tmp/dhcpc.conf");	
				}
#endif				
				if (IS_VALID_ID(g_pEmbedInfo->p2pid)) 
				 	fprintf(js_file, "udhcpc -t 10 -A 10 -b -i %s -s %s -x hostname:IPC_%s\n", 
				 		if_name, dhcpc_cfg,  g_pEmbedInfo->p2pid);
				else 
					fprintf(js_file, "udhcpc -t 10 -A 10 -b -i %s -s %s\n", if_name, dhcpc_cfg);
			}
			
		} while (0);
	}
	
	// 增加广播地址
	fprintf(js_file, "route add -host *************** dev %s\n", if_name);
	fprintf(js_file, "route add -net *************** netmask *************** %s\n", if_name);
	// 同步一次时间
	//fprintf(js_file, "ntpd -p %s -qNn&\n", TIME_CLOCK_SVR);

	// WiFi AP功能已移除，专注于双网口以太网模式

	// onvif
	fprintf(js_file, "killall -9 onvif_server\n");
	if (g_pRunSets->misc_set.onvif_enable) {
		fprintf(js_file, "/opt/app/onvif_server > /dev/null 2>&1 &\n");
	}

	fclose(js_file);

	// 改变权限并运行
	set_wpa_ready(FALSE);
	system_no_fd("chmod 777 " JS_NET_FILE "; sleep 0.5; " JS_NET_FILE "&");

	LOGI("%s, done. if=%s, dual_eth_mode=%d", __FUNCTION__, if_name, dual_eth_mode);

	// WiFi配网功能已移除，专注于双网口以太网模式
	
	return OK;
}

/**
 * 智能网络配置加载（支持不同配置文件路径）
 * @param if_name 网络接口名称
 * @param is_primary 是否为主接口
 * @return 成功=OK；失败=FAIL
 */
INT32 net_load_config_smart(LPCSTR if_name, UINT8 is_primary)
{
	if (!if_name) {
		LOGE("Invalid interface name");
		return FAIL;
	}

	// 确保网口配置独立性
	if (!net_ensure_interface_independence(if_name, "load")) {
		LOGE("Interface independence check failed for smart config: %s", if_name);
		return FAIL;
	}

	LOGI("Loading smart network configuration for %s (primary=%d)", if_name, is_primary);

	// 使用严格配置文件路径
	CHAR config_file[128];
	if (!net_get_strict_config_path(if_name, config_file)) {
		LOGE("Failed to get strict config path for smart config: %s", if_name);
		return FAIL;
	}

	// 检查配置文件是否存在
	if (!file_exist(config_file)) {
		LOGI("Smart config file %s not found, using default configuration", config_file);
		return net_load_config(if_name);
	}

	// 加载智能配置
	UINT8 is_eth1 = (stricmp(if_name, NET_ETH1) == 0) ? TRUE : FALSE;
	T_SET_NETWORK *net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

	// 从智能配置文件加载网络设置
	if (settings_load_network(config_file, net)) {
		LOGI("Loaded smart network configuration from %s", config_file);

		// 应用配置
		INT32 result = net_load_config(if_name);
		if (result == OK) {
			// 等待配置生效
			Sleep(3000);

			// 检查配置是否成功并自动保存
			CHAR current_ip[32];
			if (net_if_ready(if_name, current_ip) && strlen(current_ip) > 0) {
				LOGI("Smart config applied successfully for %s, IP: %s", if_name, current_ip);

				// 自动保存当前有效配置
				if (net_auto_save_config_on_ready(if_name)) {
					LOGI("Auto saved smart config for %s", if_name);
				}
			}

			// 保存配置到标准位置（向后兼容）
			settings_save_net(if_name);
			LOGI("Smart network configuration applied successfully for %s", if_name);
		}
		return result;
	} else {
		LOGE("Failed to load smart network configuration from %s", config_file);
		return net_load_config(if_name);  // 回退到默认配置
	}
}

/**
 * 获取智能IP分配状态信息
 * @param status_info 返回状态信息的JSON字符串指针
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_get_smart_ip_status(CHAR **status_info)
{
	if (!status_info) return FALSE;

	cJSON *root = cJSON_CreateObject();
	if (!root) return FALSE;

	// 基本信息
	cJSON_AddBoolToObject(root, "smart_ip_enabled", g_smart_ip_enabled);
	cJSON_AddBoolToObject(root, "dual_eth_mode", g_dual_eth_mode);
	cJSON_AddBoolToObject(root, "network_segment_detected", g_network_segment_detected);
	cJSON_AddBoolToObject(root, "same_segment_detected", g_same_segment_detected);
	cJSON_AddNumberToObject(root, "last_segment_check_time", g_last_segment_check_time);

	// 当前网络状态
	UINT8 eth0_carrier = net_dev_carrier(NET_ETH0);
	UINT8 eth1_carrier = net_dev_carrier(NET_ETH1);
	UINT8 eth0_ready = net_if_ready(NET_ETH0, NULL);
	UINT8 eth1_ready = net_if_ready(NET_ETH1, NULL);

	cJSON *interfaces = cJSON_CreateObject();

	// ETH0状态
	cJSON *eth0_obj = cJSON_CreateObject();
	cJSON_AddBoolToObject(eth0_obj, "carrier", eth0_carrier);
	cJSON_AddBoolToObject(eth0_obj, "ready", eth0_ready);
	cJSON_AddStringToObject(eth0_obj, "config_file", CFG_NETWORK(CFG_PATH, 0));

	if (eth0_ready) {
		CHAR ip[32];
		if (net_if_ready(NET_ETH0, ip)) {
			cJSON_AddStringToObject(eth0_obj, "ip", ip);
		}
		CHAR gateway[32];
		if (net_gw_addr(NET_ETH0, gateway)) {
			cJSON_AddStringToObject(eth0_obj, "gateway", gateway);
		}
	}
	cJSON_AddItemToObject(interfaces, "eth0", eth0_obj);

	// ETH1状态
	cJSON *eth1_obj = cJSON_CreateObject();
	cJSON_AddBoolToObject(eth1_obj, "carrier", eth1_carrier);
	cJSON_AddBoolToObject(eth1_obj, "ready", eth1_ready);
	cJSON_AddStringToObject(eth1_obj, "config_file", CFG_NETWORK(CFG_PATH, 1));
	cJSON_AddBoolToObject(eth1_obj, "disabled_due_to_same_segment",
						  g_same_segment_detected && eth1_carrier);

	if (eth1_ready) {
		CHAR ip[32];
		if (net_if_ready(NET_ETH1, ip)) {
			cJSON_AddStringToObject(eth1_obj, "ip", ip);
		}
		CHAR gateway[32];
		if (net_gw_addr(NET_ETH1, gateway)) {
			cJSON_AddStringToObject(eth1_obj, "gateway", gateway);
		}
	}
	cJSON_AddItemToObject(interfaces, "eth1", eth1_obj);

	cJSON_AddItemToObject(root, "interfaces", interfaces);

	// 智能分配策略
	cJSON *strategy = cJSON_CreateObject();
	if (g_network_segment_detected) {
		if (g_same_segment_detected) {
			cJSON_AddStringToObject(strategy, "mode", "same_segment");
			cJSON_AddStringToObject(strategy, "description",
									"Same network segment detected - ETH0 only to avoid IP conflicts");
		} else {
			cJSON_AddStringToObject(strategy, "mode", "different_segments");
			cJSON_AddStringToObject(strategy, "description",
									"Different network segments detected - both interfaces configured");
		}
	} else {
		cJSON_AddStringToObject(strategy, "mode", "detection_pending");
		cJSON_AddStringToObject(strategy, "description", "Network segment detection in progress or failed");
	}
	cJSON_AddItemToObject(root, "strategy", strategy);

	*status_info = cJSON_PrintUnformatted(root);
	cJSON_Delete(root);

	return *status_info != NULL;
}

static INT32		g_internet_state = INTERNET_STATE_UNKNOW-1;


/**
 * 域名转IP
 */
ULONG net_host_detect(LPCSTR host)
{
	ULONG 	dwIp = 0;
    INT32   fnRet;

	if (host EQU NULL  || strcmp(host, "") == 0)
		return 0;

	if (strcmp(host,"***************") == 0)
		return 0xFFFFFFFF;

	fnRet = inet_pton(AF_INET, host, &dwIp);
    if (fnRet <= 0) {
		
		struct addrinfo *result = NULL;
		struct addrinfo hints;

		bzero(&hints, sizeof (struct addrinfo));
		hints.ai_flags	= AI_CANONNAME; 	/* always return canonical name */
		hints.ai_family = AF_INET;			/* AF_UNSPEC, AF_INET, AF_INET6, etc. */
		hints.ai_socktype = SOCK_DGRAM;
		hints.ai_protocol = IPPROTO_UDP;
	
		if (getaddrinfo(host, NULL, &hints, &result) EQU 0)
		{
			struct addrinfo* res = result;
	
			/* prefer ip4 addresses */
			while (res)
			{
				if (res->ai_family == AF_INET) {
					dwIp = ((struct sockaddr_in*)(res->ai_addr))->sin_addr.s_addr;
					break;
				}
				res = res->ai_next;
			}
	
			freeaddrinfo(result);
		}

		
    }

	return dwIp;
}


// internet断网检测
LPVOID internet_abort_thread(LPVOID)
{
	SET_THREAD_NAME(); 
	LogI("running...");
	UINT32	reboot_time = NET_ABNORMAL_TIMEOUT_INTERNET_NEVER;
	INT32	reboot_countdown = reboot_time;
	INT32	inet_state = INTERNET_STATE_UNKNOW;
	INT32	delay_sec = 0;
	INT32	domain_pos = 0;
	UINT8	net_link_1_cnt = FALSE;
	LPCSTR 	domains[] = {
#if defined(GB28181_ENABLE) && (GB_PLATFORM_ID==GB_PFID_SCDX_MJ) 	// 魔镜平台
		"gateway.mj.sctel.com.cn",
		"open.mj.sctel.com.cn",
#endif
		"www.baidu.com",
		"www.qq.com",
		"www.google.com",
		"www.microsoft.com",
		"amazon.com",
		"vs98.com"
	};
	
	while (!system_quit() && !g_pRunSets->misc_set.disable_reboot) {
		if (inet_state != g_internet_state) {	// 状态改变
			inet_state = g_internet_state;
			if (g_internet_state >= INTERNET_STATE_SUCC) {
				// 连接上了
				reboot_time = NET_ABNORMAL_TIMEOUT_INTERNET_NEVER;
			}
			else if (g_internet_state EQU INTERNET_STATE_ABORT) {
				// 明确断开了
				reboot_time = NET_ABNORMAL_TIMEOUT_INTERNET;
			}
			else {
				// 未知状态
				reboot_time = NET_ABNORMAL_TIMEOUT_INTERNET_NEVER;
			}
			reboot_countdown = reboot_time;
			delay_sec = 0;
		}

		if (inet_state <= INTERNET_STATE_ABORT) {	// 中断或者未知
			do {
				if (delay_sec > 0) {	// 延时处理
					delay_sec--;
					break;
				}
				// WiFi配网模式已移除，双网口模式下无需此检查
				ULONG	uptm_first = get_app_uptime();
//				UINT32 	t_pkts[2], r_pkts[2], t_drop[2], r_drop[2], carr;
//
//				// 记得第一次网卡流量
//				net_dev_status(net_get_work_ifname(), &t_pkts[0], &r_pkts[0], &t_drop[0], &r_drop[0], &carr);
			
				/**
				 * 从未连接上internet的情况下: 
				 *	step1: 先ping网关, 
				 *	step2: 再连接有没有tcp连接
				 *		可能有nvr或者我们自己客户端或者vlc拉流, 
				 * 		这种情况检测有没有tcp连接, 如果没有或者允许重启
				 */
				if (inet_state EQU INTERNET_STATE_UNKNOW) {
					UINT8 do_succ = FALSE;

#if defined(GB28181_ENABLE) && (GB_PLATFORM_ID==GB_PFID_SCDX_MJ)
					// 四川电信不做此判断,他们有时候测试就是不插网线
#else
					// 使用有线模式,并且网线没有插入,就是单机模式,也不重启(有些客户单机跑)
					if (!net_link_1_cnt
						&& stricmp(net_get_work_ifname(),  NET_ETH0) EQU 0
						&& !net_dev_carrier(NET_ETH0)) {	
						reboot_countdown = reboot_time;
						delay_sec = 5;
						LogD("NET_ETH0 carrier=0, delay_sec=%d", delay_sec);
						break;
					}
#endif						

					// ping网关
					{
						CHAR gw_ip[32];
						if (net_gw_addr(net_get_work_ifname(), gw_ip)){
							INT32 sock_icmp = socket(PF_INET, SOCK_RAW, IPPROTO_ICMP);

							if (sock_icmp != -1){
								fcntl(sock_icmp, F_SETFL, fcntl(sock_icmp,F_GETFL,0)|O_NONBLOCK);			// hcc add 设置为非阻塞
								
								// 网关可以ping
								if (do_ping(gw_ip, sock_icmp) EQU 0) {
									LogD("ping_ok gw_ip=%s",  gw_ip);
#if defined(GB28181_ENABLE) && (GB_PLATFORM_ID==GB_PFID_SCDX_MJ)	
#elif defined(CMCC_HJQ)
#else
									// 中性版本,网络连接成功
									vsipc_set_state(VS_LED_NORMAL);	
#endif
									do_succ = TRUE;	
								}
						
								close(sock_icmp);
							}
						}
					}
					if (do_succ) {
						reboot_countdown = reboot_time;	
						delay_sec = 10;
						net_link_1_cnt = TRUE;
						break;
					}
					
					// tcp传输无延时
					INT32 fnRet = net_tcp_jam();
					LogD("net_tcp_jam=%d",  fnRet);
					if (fnRet EQU 0) {
						reboot_countdown = reboot_time;
						delay_sec = 10;
						net_link_1_cnt = TRUE;
						break;
					}
				}
				else {
					/**
					 * 这种是曾经连接上了互联网后, 中途断开, 证明可以连接公网, 只需要使用域名来解析即可;
					 * 判断依据是只要能上公网, DNS必须是通的;
					 * 即使DNS不是通的, 他可以依靠自身的平台连接能力, 触发net_on_internet(1)来中止断网的判断,
					 * 如果他不使用这种方式来中止, 就证明他不通了					 
					 */
					UINT8 do_succ = FALSE;
					
					// 检测dns解析
					if (QfLength(domains) > 0) {
						if (domain_pos >= QfLength(domains))
							domain_pos = 0; 
						//LogD("domain host=%s", domains[domain_pos]); 
						ULONG haddr = net_host_detect(domains[domain_pos]);
						if (haddr != 0){
							LogD("domain_ok gw_ip=%s:%hhu.%hhu.%hhu.%hhu", domains[domain_pos], 
								(haddr)&0xff, (haddr>>8)&0xff, (haddr>>16)&0xff, (haddr>>24)&0xff);
							do_succ = TRUE;
						}
						else {
							LogD("domain host=%s, errno=%d", domains[domain_pos], errno); 
							domain_pos++;
						}
					} 
					if (do_succ) {
						reboot_countdown = reboot_time;	
						delay_sec = 10;
						net_link_1_cnt = TRUE;
						break;
					}
				}

				// 稍停2秒
				uptm_first = get_app_uptime() - uptm_first;
				if (uptm_first < 2) {
					Sleep((2-uptm_first)*1000);
					uptm_first = 2;
				}

//				// 记录第二次网卡流量(上面的dns与ping都会生网络包)
//				net_dev_status(net_get_work_ifname(), &t_pkts[1], &r_pkts[1], &t_drop[1], &r_drop[1], &carr);
//			
//				UINT8 abnormal_status = (t_pkts[0] EQU t_pkts[1] && r_pkts[0] EQU r_pkts[1]) 
//										|| (r_drop[1] > r_drop[0]+2 || t_drop[1] > t_drop[0]+2);
//				LogD("abnormal_status=%hhu, reboot_countdown=%d",  abnormal_status, reboot_countdown);
//				if (!abnormal_status) {	// 网卡有流量交互, 配网时,重新计时
//					reboot_countdown = reboot_time;
//					delay_sec = 10;
//					break;
//				}
				
				reboot_countdown -= uptm_first; 
				if (reboot_countdown <= 0) {	// 真正意义上重启
					LOGW("internet abort, system reboot!");
					set_except_flag();
					system_reboot();
				
					LOGI("%s() exit.", __FUNCTION__);	

					END_THREAD;
				}
				LogD("inet_state=%d, reboot_countdown=%d",  inet_state, reboot_countdown);
			}while (FALSE);	
		}		
//		else {
//			if (QfLength(domains) > 0) {
//				if (domain_pos >= QfLength(domains))
//					domain_pos = 0; 
//				LogD("domain host=%s", domains[domain_pos]); 
//				ULONG haddr = net_host_detect(domains[domain_pos]);
//				if (haddr != 0){
//					LogD("domain_ok gw_ip=%s:%hhu.%hhu.%hhu.%hhu", domains[domain_pos], 
//						(haddr)&0xff, (haddr>>8)&0xff, (haddr>>16)&0xff, (haddr>>24)&0xff);
//				}
//				else {
//					LogD("domain host=%s, errno=%d", domains[domain_pos], errno); 
//					domain_pos++;
//				}
//			} 
//		}
		
		Sleep(1000);	 
//		LogD("inet_state=%d", inet_state);
	}

	LOGI("%s() exit.", __FUNCTION__);	

	END_THREAD;
}


/**
 * 连接internet
 * @param  state	=1则连接internet；=0则断开与internet连接, =-1则是启动的时候默认为internet未连接
 * @return         返回TRUE表示开启了时间同步, FALSE表示没有开启时间同步
 */
UINT8 net_on_internet(INT32 state)
{
#ifndef LT_DENGHONG
#ifdef CMCC_HJQ
	;;//和家亲不处理
#else
	vsipc_set_state(state EQU 1 ? VS_LED_NORMAL : VS_LED_NET_FAIL);
#endif
#endif
	
	LogI("cur_state=%d, new_state=%d", g_internet_state, state);
	if (g_internet_state != state) {
		static UINT8 s_internet_detect = FALSE;
		
		g_internet_state = state;
		if (!s_internet_detect && !g_pRunSets->misc_set.disable_reboot) {

#ifdef NET_ABNORMAL_REBOOT_IPC		
		    if (PTHREAD_CREATE(NULL, NULL, internet_abort_thread, NULL)) {
		        LOGE("PTHREAD_CREATE(internet_abort_thread) = %d", errno);
			} 
			else {
				s_internet_detect = TRUE;
			}
#endif					
		}
	}

	// 上线互联网 
	if (state EQU INTERNET_STATE_SUCC)
	{
		static UINT8	s_login = FALSE;

		if (!s_login) {
			s_login = TRUE;
				
			// 连接上网校时
			sync_time(TIME_CLOCK_SVR, FALSE);
			
			return TRUE;
		}
	}

	return FALSE;
}


/** 
 * 判断是否成功连接INTERNET
 * @return  成功返回TRUE, 失败返回FALSE
 */
UINT8 net_internet_ready()
{
	return g_internet_state EQU INTERNET_STATE_SUCC ? TRUE:FALSE;
}


/**
 * 得到网络工作媒介
 * 返回: 0表示
 */
INT32 net_work_interface()
{
	return g_if_save_state;
}

/**
 * 得到工作网口名称
 */
LPCSTR net_get_work_ifname()
{
	if (strlen(g_cur_if_name) EQU 0)
		return NET_ETH0;

	return g_cur_if_name;
}

/**
 * 域名转IP
 */
ULONG net_host_2_addr(LPCSTR host)
{
	ULONG 	dwIp = 0;
    INT32   fnRet;

	if (host EQU NULL  || strcmp(host, "") == 0)
		return 0;

	if (strcmp(host,"***************") == 0)
		return 0xFFFFFFFF;

	fnRet = inet_pton(AF_INET, host, &dwIp);
    if (fnRet <= 0) {
		
		struct addrinfo *result = NULL;
		struct addrinfo hints;

		bzero(&hints, sizeof (struct addrinfo));
		hints.ai_flags	= AI_CANONNAME; 	/* always return canonical name */
		hints.ai_family = AF_INET;			/* AF_UNSPEC, AF_INET, AF_INET6, etc. */
		hints.ai_socktype = 0;				/* 0, SOCK_STREAM, SOCK_DGRAM, etc. */
//		hints.ai_family = AF_UNSPEC;		/* (在res->ai_family判断地址类型) */ 
		// 不论是tcp还是udp, 如果不通, 均是卡10秒返回
//		hints.ai_socktype = SOCK_STREAM;
//		hints.ai_protocol = IPPROTO_TCP;
//		hints.ai_socktype = SOCK_DGRAM;
//		hints.ai_protocol = IPPROTO_UDP;
	
		if (getaddrinfo(host, NULL, &hints, &result) EQU 0)
		{
			struct addrinfo* res = result;
	
			/* prefer ip4 addresses */
			while (res)
			{
				if (res->ai_family == AF_INET) {
					dwIp = ((struct sockaddr_in*)(res->ai_addr))->sin_addr.s_addr;
					break;
				}
				res = res->ai_next;
			}
	
			freeaddrinfo(result);
		}

		
    }

	return dwIp;
}


//=================================================================================================
// ping----begin
//=================================================================================================


//#define PING_OUT


u_int16_t Compute_cksum(struct icmp *pIcmp)
{
	u_int16_t *data = (u_int16_t *)pIcmp;
	int len = ICMP_LEN;
	u_int32_t sum = 0;
	
	while (len > 1)
	{
		sum += *data++;
		len -= 2;
	}
	if (1 == len)
	{
		u_int16_t tmp = *data;
		tmp &= 0xff00;
		sum += tmp;
	}

	//ICMP校验和带进位
	while (sum >> 16)
		sum = (sum >> 16) + (sum & 0x0000ffff);
	sum = ~sum;
	
	return sum;
}

void SetICMP(u_int16_t seq, char *SendBuffer)
{
	struct icmp *pIcmp;
	struct timeval *pTime;

	pIcmp = (struct icmp*)SendBuffer;
	
	/* 类型和代码分别为ICMP_ECHO,0代表请求回送 */
	pIcmp->icmp_type = ICMP_ECHO;
	pIcmp->icmp_code = 0;
	pIcmp->icmp_cksum = 0;							//校验和
	pIcmp->icmp_seq = seq;							//序号
	pIcmp->icmp_id = getpid();						//取进程号作为标志
	pTime = (struct timeval *)pIcmp->icmp_data;
	gettimeofday(pTime, NULL);						//数据段存放发送时间
	pIcmp->icmp_cksum = Compute_cksum(pIcmp);
}

int unpack(char *RecvBuffer)
{
	struct ip *Ip = (struct ip *)RecvBuffer;
	struct icmp *Icmp;
	int ipHeadLen;

	ipHeadLen = Ip->ip_hl << 2;	//ip_hl字段单位为4字节
	Icmp = (struct icmp *)(RecvBuffer + ipHeadLen);

	//判断接收到的报文是否是自己所发报文的响应
	if ((Icmp->icmp_type == ICMP_ECHOREPLY) && Icmp->icmp_id == getpid())
	{
		//struct timeval *SendTime = (struct timeval *)Icmp->icmp_data;

#ifdef PING_OUT
		printf("%u bytes from %s: icmp_seq=%u ttl=%u \n",
			ntohs(Ip->ip_len) - ipHeadLen,
			inet_ntoa(Ip->ip_src),
			Icmp->icmp_seq,
			Ip->ip_ttl);
#endif

		return PING_OK;
	}
		
	return PING_ERR;
}

int SendPacket(int sock_icmp, struct sockaddr_in *dest_addr, int *nSend, char *SendBuffer)
{
	memset(SendBuffer, 0, PING_BUFFER_SIZE);
	SetICMP(*nSend, SendBuffer);
	if (sendto(sock_icmp, SendBuffer, ICMP_LEN, 0,
		(struct sockaddr *)dest_addr, sizeof(struct sockaddr_in)) < 0)
	{
		perror("sendto");
		return PING_ERR;
	}
	return PING_OK;
}


int select_recvfrom(int fd, int sec)
{
	fd_set rset;
	struct timeval tv;
 
	FD_ZERO(&rset);
	FD_SET(fd, &rset);
 
	tv.tv_sec = sec;
	tv.tv_usec = 0;
 
	return (select(fd+1, &rset, NULL, NULL, &tv));
}

int RecvePacket(int sock_icmp, struct sockaddr_in *dest_addr, int *nRecv, char *RecvBuffer)
{
	UNUSED(nRecv);
	int RecvBytes = 0;
	int addrlen = sizeof(struct sockaddr_in);

	memset(RecvBuffer, 0, PING_BUFFER_SIZE);
	
	// 用select来recvfrom设置超时，因前面设备了sock_icmp为非阻塞，这里需要等待
	select_recvfrom(sock_icmp, PING_WAIT_TIME);

	if ((RecvBytes = recvfrom(sock_icmp, RecvBuffer, PING_BUFFER_SIZE,
			0, (struct sockaddr *)dest_addr, (socklen_t*)&addrlen)) < 0)
	{
		//perror("recvfrom");
		return PING_ERR;
	}

#if 1		
	// 不处理回环
	if (unpack(RecvBuffer) < 0)
	{
		return PING_ERR; 
	}
#endif

	return PING_OK;
}

//设置非阻塞
static void setnonblocking(int sockfd)
{
    int flag = fcntl(sockfd, F_GETFL, 0);
    if (flag < 0) 
    {
        printf("fcntl F_GETFL fail\n");
        return;
    }
    if (fcntl(sockfd, F_SETFL, flag | O_NONBLOCK) < 0) 
    {
        printf("fcntl F_SETFL fail\n");
    }
} 

// ping功能入口函数；传入IP地址
// 成功返回0，失败返回-1
int do_ping(char *ip, int sock_icmp)
{
	in_addr_t inaddr;					//ip地址（网络字节序）
	struct sockaddr_in dest_addr; 		//IPv4专用socket地址,保存目的地址
	char RecvBuffer[PING_BUFFER_SIZE] = {0};
	char SendBuffer[PING_BUFFER_SIZE] = {0};
	int nRecv = 0;	//实际接收到的报文数
	int nSend = 0;
	int recv_err = 0;

	dest_addr.sin_family = AF_INET;
	
	/* 将点分十进制ip地址转换为网络字节序 */
	if ((inaddr = inet_addr(ip)) == INADDR_NONE)
	{
		dest_addr.sin_addr.s_addr = net_host_2_addr(ip);
		if (dest_addr.sin_addr.s_addr EQU 0)
			return -1;
	}
	else
	{
		memcpy(&dest_addr.sin_addr, &inaddr, sizeof(struct in_addr));
	}

#ifdef PING_OUT
	printf("PING %s", ip);
	printf("(%s) %d bytes of data.\n", inet_ntoa(dest_addr.sin_addr), ICMP_LEN);
#endif

	while (nSend <= PING_SEND_NUM)
	{
		int ret;
		
		if (SendPacket(sock_icmp, &dest_addr, &nSend, SendBuffer) < 0)
			return -1;
		else
			nSend++;
			
		// 如果为回环测试(127.0.0.1)，则重新获取；如果传输失败，返回-2
		ret = RecvePacket(sock_icmp, &dest_addr, &nRecv, RecvBuffer);
		if (ret < 0){
			recv_err++;

			if (recv_err > PING_SEND_NUM)
			{
#ifdef PING_OUT			
				PRI_PING("ALL ERR", ip, nSend, nRecv);
#endif
				return PING_ERR;
			}
		}
		else			// 接收成功
		{
			nRecv++;
		}

		Sleep(1000);
	}
	
	
//	PRI_PING("ALL Succ", ip, nSend, nRecv);

	return PING_OK;
}


/** 
 * 网络设备状态
 * @param  netif  网络设备名
 * @param  t_pkts  tx字节数
 * @param  r_pkts  rx字节数
 * @param  t_drop  tx掉包数
 * @param  r_drop  rx掉包数
 * @param  carr  插入次数
 * @return  成功TRUE;失败FALSE
 */
UINT8 net_dev_status(LPCSTR netif, 	UINT32 *t_pkts, UINT32 *r_pkts, UINT32 *t_drop, UINT32 *r_drop, UINT32 *carr)
{
	*t_pkts = 0;
	*r_pkts = 0;
	*t_drop = 0;
	*r_drop = 0; 
	*carr = 1;

	if (netif EQU NULL)
		return FALSE;
	
	FILE *pfile = fopen("/proc/net/dev", "r");
	if (NULL != pfile) {
		LPSTR	line = NULL;
		size_t	nums = 0;		
		
		while (getline(&line, &nums, pfile) != -1){
			if (strstr(line, netif)) {

				LPSTR 	str = strstr(line, netif)+strlen(netif)+1;

				if (sscanf(str, "%*s%u%*s%u%*s%*s%*s%*s%*s%u%*s%u", r_pkts, r_drop, t_pkts, t_drop) > 0) {		
					//LOGW_NF("%u  %u  %u  %u", *r_pkts, *r_drop, *t_pkts, *t_drop);
				}	
				
				break;
			}

			free(line);
			line = NULL;
			nums=0;
			
		}

		// 读取失败 也需要释放
		free(line);
		line = NULL;
		nums = 0;
	
		fclose(pfile);
		
	}
	else {
		return FALSE;
	}

	{
		char	file_name[128];

		sprintf(file_name, "/sys/class/net/%s/carrier_changes", netif);
		pfile = fopen(file_name, "r");
		if (NULL != pfile) {
			fscanf(pfile, "%d", carr);
			fclose(pfile);
		}
	}

	return TRUE;

}


float net_get_pktlossrate(char* netif)
{
	float pktlossrate = 0;
	UINT32 t_pkts, r_pkts, t_drop, r_drop, carr;

	if (net_dev_status(netif, &t_pkts, &r_pkts, &t_drop, &r_drop, &carr)) {

		LOGW_NF("%u  %u  %u  %u", r_pkts, r_drop, t_pkts, t_drop);
		pktlossrate = (r_drop + t_drop)*100.0/(r_pkts+t_pkts);
	}	

	return pktlossrate;
}

/**
 * 判断网络设备ip是否冲突
 *  ip 检验的IP
 *  valid_num 包校验次数(1-10)
 *  time_out 超时时长（秒) 
 *	if_name 如果if_name!=NULL,将指定网卡进行检测
 * @return   返回1表示可用；返回0表示请求失败 -1表示冲突	
 */
INT32 net_ip_conflict_check(LPSTR ip, UINT32 valid_num, UINT32 time_out, LPCSTR if_name)
{
	CHAR cmd[128];
	CHAR res[32];
	int num = -1;
	INT32 check_res=0;

	do{
		if(ip EQU NULL)
		{
			LogE("system_ip_conflict_check() IP not exit!");
			break;
		}
		
		if(if_name != NULL && !net_dev_carrier(if_name))
		{
			LogE("system_ip_conflict_check() %s not ready!", if_name);
			break;
		}

		QfSet0(cmd,sizeof(cmd));
		sprintf(cmd,"arping -I %s -w %d -c %d %s | awk"
			" '/Received/ {num=$2;print num}'|tr -d '\n'",
			(if_name EQU NULL)?net_get_work_ifname():if_name, QF_MAX(2,QF_MIN(valid_num,10)), QF_MAX(2,QF_MIN(time_out,10)), ip);
		LogI("ip check:/n%s/n", cmd);
		FILE *fp = popen(cmd, "r");
		if(fp)
		{
			QfSet0(res,sizeof(res));
			fread(res, 1, sizeof(res) - 1, fp);
			pclose(fp);
			LOGI("recv:%s", res);
    		if(sscanf(res, "%d", &num))
			{	//有回复,表示IP不可用
    			if(num EQU valid_num)
    			{
    				check_res = -1;
    			}else if(num EQU 0){
    				check_res = 1;
				}
    		}
		}else
		{
			LOGE("popen %s error",cmd);
		}
		
	}while(FALSE);
		
	return check_res;
}



int get_wlan0_quality(char* netif)
{
	int quality = 0;

	FILE *pfile = fopen("/proc/net/wireless", "r");
	if (NULL != pfile) {
		char	*line = NULL;
		size_t	nums = 0;		
		
		while (getline(&line, &nums, pfile) != -1){

			if (strstr(line, netif)) {

				char link[32];
				char* str = strstr(line, netif)+strlen(netif)+1;

				if (sscanf(str, "%*s%*s%s",  link) > 0) {

					LOGW_NF("%d\n", atoi(link));
					quality = atoi(link);  // 返回信号强度
				}
				
				break;
			}
	
			free(line);
			line = NULL;
			nums=0;
			
		}

		// 读取失败 也需要释放
		free(line);
		line = NULL;
		fclose(pfile);
		
	}

	return quality;
}











//=================================================================================================
// ping----end
//=================================================================================================

